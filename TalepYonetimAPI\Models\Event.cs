using System.ComponentModel.DataAnnotations;

namespace TalepYonetimAPI.Models
{
    public class Event
    {
        public int Id { get; set; }
        
        public int AdminId { get; set; }
        
        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;
        
        public string? Description { get; set; }
        
        public DateTime StartDate { get; set; }
        
        public DateTime EndDate { get; set; }
        
        [StringLength(200)]
        public string? Location { get; set; }
        
        [StringLength(50)]
        public string? EventType { get; set; } // 'Meeting', 'Training', 'Conference', 'Holiday' vs.
        
        public bool IsAllDay { get; set; } = false;
        
        [StringLength(7)]
        public string Color { get; set; } = "#007bff"; // Takvimde renk için hex kod
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
        
        // Navigation properties
        public virtual User Admin { get; set; } = null!;
    }
}
