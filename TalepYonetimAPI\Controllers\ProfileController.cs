using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TalepYonetimAPI.Data;
using TalepYonetimAPI.Models.DTOs;

namespace TalepYonetimAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ProfileController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<ProfileController> _logger;

        public ProfileController(ApplicationDbContext context, ILogger<ProfileController> logger)
        {
            _context = context;
            _logger = logger;
        }

        [HttpGet("{userId}")]
        public async Task<IActionResult> GetProfile(int userId)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    return NotFound(new { message = "Kullanıcı bulunamadı." });
                }

                return Ok(new
                {
                    id = user.Id,
                    employeeNumber = user.EmployeeNumber,
                    firstName = user.FirstName,
                    lastName = user.LastName,
                    department = user.Department,
                    userType = user.UserType,
                    createdAt = user.CreatedAt,
                    isActive = user.IsActive
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting profile");
                return StatusCode(500, new { message = "Profil getirilirken bir hata oluştu." });
            }
        }

        [HttpPut("{userId}")]
        public async Task<IActionResult> UpdateProfile(int userId, [FromBody] UpdateProfileDto updateProfileDto)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    return NotFound(new { message = "Kullanıcı bulunamadı." });
                }

                user.FirstName = updateProfileDto.FirstName;
                user.LastName = updateProfileDto.LastName;
                user.Department = updateProfileDto.Department;

                await _context.SaveChangesAsync();

                return Ok(new { message = "Profil başarıyla güncellendi." });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while updating profile");
                return StatusCode(500, new { message = "Profil güncellenirken bir hata oluştu." });
            }
        }

        [HttpGet("{userId}/settings")]
        public async Task<IActionResult> GetUserSettings(int userId)
        {
            try
            {
                var settings = await _context.UserSettings
                    .FirstOrDefaultAsync(s => s.UserId == userId);

                if (settings == null)
                {
                    // Varsayılan ayarları oluştur
                    settings = new Models.UserSettings
                    {
                        UserId = userId
                    };
                    _context.UserSettings.Add(settings);
                    await _context.SaveChangesAsync();
                }

                return Ok(new
                {
                    id = settings.Id,
                    theme = settings.Theme,
                    language = settings.Language,
                    emailNotifications = settings.EmailNotifications,
                    pushNotifications = settings.PushNotifications
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting user settings");
                return StatusCode(500, new { message = "Kullanıcı ayarları getirilirken bir hata oluştu." });
            }
        }

        [HttpPut("{userId}/settings")]
        public async Task<IActionResult> UpdateUserSettings(int userId, [FromBody] dynamic settingsData)
        {
            try
            {
                var settings = await _context.UserSettings
                    .FirstOrDefaultAsync(s => s.UserId == userId);

                if (settings == null)
                {
                    settings = new Models.UserSettings
                    {
                        UserId = userId
                    };
                    _context.UserSettings.Add(settings);
                }

                // JSON'dan ayarları güncelle
                if (settingsData.theme != null)
                    settings.Theme = settingsData.theme;
                
                if (settingsData.language != null)
                    settings.Language = settingsData.language;
                
                if (settingsData.emailNotifications != null)
                    settings.EmailNotifications = settingsData.emailNotifications;
                
                if (settingsData.pushNotifications != null)
                    settings.PushNotifications = settingsData.pushNotifications;

                settings.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();

                return Ok(new { message = "Ayarlar başarıyla güncellendi." });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while updating user settings");
                return StatusCode(500, new { message = "Ayarlar güncellenirken bir hata oluştu." });
            }
        }

        [HttpGet("{userId}/stats")]
        public async Task<IActionResult> GetUserStats(int userId)
        {
            try
            {
                var totalRequests = await _context.Requests.CountAsync(r => r.UserId == userId);
                var answeredRequests = await _context.Requests.CountAsync(r => r.UserId == userId && r.Status == "Answered");
                var pendingRequests = await _context.Requests.CountAsync(r => r.UserId == userId && r.Status == "Pending");
                var favoriteRequests = await _context.FavoriteRequests.CountAsync(f => f.UserId == userId);
                var completedTodos = await _context.TodoItems.CountAsync(t => t.UserId == userId && t.IsCompleted);
                var pendingTodos = await _context.TodoItems.CountAsync(t => t.UserId == userId && !t.IsCompleted);
                var answeredSurveys = await _context.SurveyResponses.CountAsync(sr => sr.UserId == userId);

                return Ok(new
                {
                    totalRequests,
                    answeredRequests,
                    pendingRequests,
                    favoriteRequests,
                    completedTodos,
                    pendingTodos,
                    answeredSurveys
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting user stats");
                return StatusCode(500, new { message = "Kullanıcı istatistikleri getirilirken bir hata oluştu." });
            }
        }
    }
}
