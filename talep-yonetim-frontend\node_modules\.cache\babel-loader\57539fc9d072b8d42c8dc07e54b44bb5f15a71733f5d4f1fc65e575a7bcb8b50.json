{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = 'https://localhost:7119/api';\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Auth API calls\nexport const authAPI = {\n  login: (employeeNumber, password) => api.post('/auth/login', {\n    employeeNumber,\n    password\n  }),\n  register: userData => api.post('/auth/register', userData)\n};\n\n// Requests API calls\nexport const requestsAPI = {\n  create: (requestData, userId) => api.post(`/requests?userId=${userId}`, requestData),\n  getUserRequests: userId => api.get(`/requests/user/${userId}`),\n  getAllForAdmin: () => api.get('/requests/admin')\n};\n\n// Responses API calls\nexport const responsesAPI = {\n  create: (responseData, adminId) => api.post(`/responses?adminId=${adminId}`, responseData),\n  getByRequest: requestId => api.get(`/responses/request/${requestId}`)\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "api", "create", "baseURL", "headers", "authAPI", "login", "employeeNumber", "password", "post", "register", "userData", "requestsAPI", "requestData", "userId", "getUserRequests", "get", "getAllForAdmin", "responsesAPI", "responseData", "adminId", "getByRequest", "requestId"], "sources": ["C:/Users/<USER>/Desktop/metin_kayit/talep-yonetim-frontend/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = 'https://localhost:7119/api';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Auth API calls\nexport const authAPI = {\n  login: (employeeNumber, password) =>\n    api.post('/auth/login', { employeeNumber, password }),\n  \n  register: (userData) =>\n    api.post('/auth/register', userData),\n};\n\n// Requests API calls\nexport const requestsAPI = {\n  create: (requestData, userId) =>\n    api.post(`/requests?userId=${userId}`, requestData),\n  \n  getUserRequests: (userId) =>\n    api.get(`/requests/user/${userId}`),\n  \n  getAllForAdmin: () =>\n    api.get('/requests/admin'),\n};\n\n// Responses API calls\nexport const responsesAPI = {\n  create: (responseData, adminId) =>\n    api.post(`/responses?adminId=${adminId}`, responseData),\n  \n  getByRequest: (requestId) =>\n    api.get(`/responses/request/${requestId}`),\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAG,4BAA4B;AAEjD,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC;EACvBC,OAAO,EAAEH,YAAY;EACrBI,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMC,OAAO,GAAG;EACrBC,KAAK,EAAEA,CAACC,cAAc,EAAEC,QAAQ,KAC9BP,GAAG,CAACQ,IAAI,CAAC,aAAa,EAAE;IAAEF,cAAc;IAAEC;EAAS,CAAC,CAAC;EAEvDE,QAAQ,EAAGC,QAAQ,IACjBV,GAAG,CAACQ,IAAI,CAAC,gBAAgB,EAAEE,QAAQ;AACvC,CAAC;;AAED;AACA,OAAO,MAAMC,WAAW,GAAG;EACzBV,MAAM,EAAEA,CAACW,WAAW,EAAEC,MAAM,KAC1Bb,GAAG,CAACQ,IAAI,CAAC,oBAAoBK,MAAM,EAAE,EAAED,WAAW,CAAC;EAErDE,eAAe,EAAGD,MAAM,IACtBb,GAAG,CAACe,GAAG,CAAC,kBAAkBF,MAAM,EAAE,CAAC;EAErCG,cAAc,EAAEA,CAAA,KACdhB,GAAG,CAACe,GAAG,CAAC,iBAAiB;AAC7B,CAAC;;AAED;AACA,OAAO,MAAME,YAAY,GAAG;EAC1BhB,MAAM,EAAEA,CAACiB,YAAY,EAAEC,OAAO,KAC5BnB,GAAG,CAACQ,IAAI,CAAC,sBAAsBW,OAAO,EAAE,EAAED,YAAY,CAAC;EAEzDE,YAAY,EAAGC,SAAS,IACtBrB,GAAG,CAACe,GAAG,CAAC,sBAAsBM,SAAS,EAAE;AAC7C,CAAC;AAED,eAAerB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}