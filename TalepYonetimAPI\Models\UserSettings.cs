using System.ComponentModel.DataAnnotations;

namespace TalepYonetimAPI.Models
{
    public class UserSettings
    {
        public int Id { get; set; }
        
        public int UserId { get; set; }
        
        [StringLength(20)]
        public string Theme { get; set; } = "Light"; // 'Light', 'Dark'
        
        [StringLength(10)]
        public string Language { get; set; } = "TR"; // 'TR', 'EN'
        
        public bool EmailNotifications { get; set; } = true;
        
        public bool PushNotifications { get; set; } = true;
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
        
        // Navigation properties
        public virtual User User { get; set; } = null!;
    }
}
