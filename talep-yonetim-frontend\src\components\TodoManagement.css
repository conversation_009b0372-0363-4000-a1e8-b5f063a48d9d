.todo-management-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.todo-management-header {
  text-align: center;
  margin-bottom: 2rem;
  padding: 2rem;
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border-radius: 16px;
  border-left: 5px solid var(--tunas-accent);
}

.todo-management-header h2 {
  margin: 0 0 0.5rem 0;
  color: var(--tunas-primary);
  font-size: 2rem;
}

.todo-management-header p {
  margin: 0;
  color: var(--tunas-gray);
  font-size: 1.1rem;
}

.loading {
  text-align: center;
  padding: 3rem;
  font-size: 1.2rem;
  color: var(--tunas-gray);
}

.error-message {
  background-color: #fef2f2;
  color: #dc2626;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  border-left: 4px solid #dc2626;
}

.todo-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border-left: 5px solid var(--tunas-secondary);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-card.urgent {
  border-left-color: #dc2626;
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: bold;
  color: var(--tunas-primary);
  margin-bottom: 0.5rem;
  display: block;
}

.stat-card.urgent .stat-number {
  color: #dc2626;
}

.stat-label {
  color: var(--tunas-gray);
  font-size: 1rem;
  font-weight: 500;
}

.filters-section {
  display: flex;
  gap: 2rem;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-group label {
  font-weight: 500;
  color: var(--tunas-primary);
  min-width: 60px;
}

.filter-select {
  padding: 0.5rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.filter-select:focus {
  outline: none;
  border-color: var(--tunas-accent);
}

.filter-info {
  margin-left: auto;
  color: var(--tunas-gray);
  font-weight: 500;
}

.no-todos {
  text-align: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.no-todos-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-todos h3 {
  color: var(--tunas-primary);
  margin-bottom: 1rem;
}

.no-todos p {
  color: var(--tunas-gray);
  max-width: 400px;
  margin: 0 auto;
  line-height: 1.6;
}

.todos-list {
  display: grid;
  gap: 1.5rem;
}

.todo-card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border-left: 5px solid var(--tunas-secondary);
  transition: all 0.3s ease;
}

.todo-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.todo-card.completed {
  opacity: 0.8;
  border-left-color: var(--tunas-accent);
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
}

.todo-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;
}

.todo-status {
  font-size: 1.5rem;
  margin-top: 0.25rem;
}

.todo-info {
  flex: 1;
}

.todo-info h3 {
  margin: 0 0 0.5rem 0;
  color: var(--tunas-primary);
  font-size: 1.3rem;
}

.todo-card.completed .todo-info h3 {
  text-decoration: line-through;
  opacity: 0.7;
}

.todo-meta {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.user-info,
.department {
  color: var(--tunas-gray);
  font-size: 0.9rem;
}

.todo-priority {
  margin-top: 0.25rem;
}

.priority-badge {
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
}

.todo-description {
  margin-bottom: 1rem;
  padding: 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 8px;
  border-left: 3px solid var(--tunas-secondary);
}

.todo-description p {
  margin: 0;
  color: var(--tunas-dark);
  line-height: 1.6;
}

.todo-dates {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

.date-item {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.date-label {
  color: var(--tunas-primary);
  font-weight: 500;
  font-size: 0.9rem;
}

.date-item span:last-child {
  color: var(--tunas-gray);
  font-size: 0.9rem;
}

.overdue {
  color: #dc2626 !important;
  font-weight: 500;
}

/* Responsive */
@media (max-width: 768px) {
  .todo-management-container {
    padding: 1rem 0.5rem;
  }
  
  .todo-management-header {
    padding: 1.5rem;
  }
  
  .todo-management-header h2 {
    font-size: 1.5rem;
  }
  
  .todo-stats {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
  }
  
  .stat-card {
    padding: 1.5rem;
  }
  
  .stat-number {
    font-size: 2rem;
  }
  
  .filters-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .filter-info {
    margin-left: 0;
  }
  
  .todo-header {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .todo-meta {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .todo-dates {
    flex-direction: column;
    gap: 0.5rem;
  }
}
