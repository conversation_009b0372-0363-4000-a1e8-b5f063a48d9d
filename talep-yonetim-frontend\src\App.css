.App {
  min-height: 100vh;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.loading {
  font-size: 1.2rem;
  color: #666;
}

/* TÜNAŞ Tema Renkleri */
:root {
  --tunas-primary: #1e3a8a;
  --tunas-secondary: #3b82f6;
  --tunas-accent: #10b981;
  --tunas-dark: #1f2937;
  --tunas-light: #f8fafc;
  --tunas-gray: #6b7280;
  --tunas-red: #ef4444;
  --tunas-orange: #f59e0b;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: var(--tunas-light);
}
