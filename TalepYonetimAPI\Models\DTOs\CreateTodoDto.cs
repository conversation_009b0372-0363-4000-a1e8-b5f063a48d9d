using System.ComponentModel.DataAnnotations;

namespace TalepYonetimAPI.Models.DTOs
{
    public class CreateTodoDto
    {
        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;
        
        public string? Description { get; set; }
        
        [StringLength(20)]
        public string Priority { get; set; } = "Normal";
        
        public DateTime? DueDate { get; set; }
    }
}
