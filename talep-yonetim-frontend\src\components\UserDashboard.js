import React, { useState, useEffect } from 'react';
import { requestsAPI } from '../services/api';
import { useAuth } from '../context/AuthContext';
import CreateRequest from './CreateRequest';
import Navbar from './Navbar';
import Footer from './Footer';
import AboutTunas from './AboutTunas';
import FavoriteRequests from './FavoriteRequests';
import TodoList from './TodoList';
import Surveys from './Surveys';
import Announcements from './Announcements';
import EventCalendar from './EventCalendar';
import './UserDashboard.css';

const UserDashboard = () => {
  const [requests, setRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showAbout, setShowAbout] = useState(false);
  const [activeView, setActiveView] = useState('my-requests'); // Başlangıç: Taleplerim
  const [error, setError] = useState('');
  const { user } = useAuth();

  useEffect(() => {
    fetchUserRequests();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const fetchUserRequests = async () => {
    try {
      setLoading(true);
      const response = await requestsAPI.getUserRequests(user.id);
      setRequests(response.data);
    } catch (error) {
      setError('Talepler yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const handleRequestCreated = () => {
    setShowCreateForm(false);
    fetchUserRequests();
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('tr-TR');
  };

  // Navbar'dan gelen navigasyon olaylarını dinle
  const handleNavigation = (view) => {
    setActiveView(view);
  };

  const renderNavigation = () => {
    const navItems = [
      { key: 'my-requests', icon: '📋', label: 'Taleplerim' },
      { key: 'favorite-requests', icon: '⭐', label: 'Favori Talepler' },
      { key: 'todo-list', icon: '✅', label: 'Yapılacaklar' },
      { key: 'surveys', icon: '📝', label: 'Anketler' },
      { key: 'announcements', icon: '📰', label: 'Duyurular' },
      { key: 'events', icon: '📅', label: 'Etkinlik Takvimi' }
    ];

    return (
      <div className="dashboard-navigation">
        <div className="nav-tabs">
          {navItems.map(item => (
            <button
              key={item.key}
              onClick={() => setActiveView(item.key)}
              className={`nav-tab ${activeView === item.key ? 'active' : ''}`}
            >
              <span className="nav-icon">{item.icon}</span>
              <span className="nav-label">{item.label}</span>
            </button>
          ))}
        </div>
      </div>
    );
  };

  const renderContent = () => {
    switch (activeView) {
      case 'favorite-requests':
        return <FavoriteRequests />;
      case 'todo-list':
        return <TodoList />;
      case 'surveys':
        return <Surveys />;
      case 'announcements':
        return <Announcements />;
      case 'events':
        return <EventCalendar />;
      case 'my-requests':
      default:
        return (
          <div className="dashboard-content">
            <div className="dashboard-actions">
              <button
                onClick={() => setShowCreateForm(true)}
                className="create-request-btn"
              >
                Yeni Talep Oluştur
              </button>
            </div>

            {showCreateForm && (
              <CreateRequest
                onClose={() => setShowCreateForm(false)}
                onRequestCreated={handleRequestCreated}
              />
            )}

            <div className="requests-section">
              <h2>Taleplerim</h2>

              {error && <div className="error-message">{error}</div>}

              {loading ? (
                <div className="loading">Talepler yükleniyor...</div>
              ) : requests.length === 0 ? (
                <div className="no-requests">Henüz hiç talebiniz bulunmuyor.</div>
              ) : (
                <div className="requests-list">
                  {requests.map(request => (
                    <div key={request.id} className="request-card">
                      <div className="request-header">
                        <h3>{request.title}</h3>
                        <span className={`status ${request.status.toLowerCase()}`}>
                          {request.status === 'Pending' ? 'Beklemede' : 'Cevaplanmış'}
                        </span>
                      </div>

                      <div className="request-content">
                        <p><strong>İçerik:</strong> {request.content}</p>
                        {request.summary && (
                          <p><strong>AI Özeti:</strong> {request.summary}</p>
                        )}
                        <p><strong>Oluşturulma Tarihi:</strong> {formatDate(request.createdAt)}</p>
                      </div>

                      {request.responses && request.responses.length > 0 && (
                        <div className="responses-section">
                          <h4>Admin Cevapları:</h4>
                          {request.responses.map(response => (
                            <div key={response.id} className="response-item">
                              <p>{response.content}</p>
                              <small>
                                {response.adminName} - {formatDate(response.createdAt)}
                              </small>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        );
    }
  };

  return (
    <div className="user-dashboard">
      <Navbar onShowAbout={() => setShowAbout(true)} />

      {renderNavigation()}

      <main className="dashboard-main">
        {renderContent()}
      </main>

      <Footer />

      {showAbout && (
        <AboutTunas onClose={() => setShowAbout(false)} />
      )}
    </div>
  );
};

export default UserDashboard;
