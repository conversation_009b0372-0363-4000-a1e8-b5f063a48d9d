.todo-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.todo-header {
  text-align: center;
  margin-bottom: 2rem;
  padding: 2rem;
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border-radius: 16px;
  border-left: 5px solid var(--tunas-accent);
}

.todo-header h2 {
  margin: 0 0 0.5rem 0;
  color: var(--tunas-primary);
  font-size: 2rem;
}

.todo-header p {
  margin: 0 0 1.5rem 0;
  color: var(--tunas-gray);
  font-size: 1.1rem;
}

.add-todo-btn {
  background: var(--tunas-accent);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.add-todo-btn:hover {
  background: #059669;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.loading {
  text-align: center;
  padding: 3rem;
  font-size: 1.2rem;
  color: var(--tunas-gray);
}

.error-message {
  background-color: #fef2f2;
  color: #dc2626;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  border-left: 4px solid #dc2626;
}

.add-todo-form {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 2rem;
  border-left: 5px solid var(--tunas-accent);
}

.add-todo-form h3 {
  margin: 0 0 1.5rem 0;
  color: var(--tunas-primary);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--tunas-primary);
  font-weight: 500;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--tunas-accent);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.submit-btn {
  background: var(--tunas-accent);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.submit-btn:hover {
  background: #059669;
}

.cancel-btn {
  background: #6b7280;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  background: #4b5563;
}

.todo-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-item {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border-left: 4px solid var(--tunas-secondary);
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: bold;
  color: var(--tunas-primary);
  margin-bottom: 0.5rem;
}

.stat-label {
  color: var(--tunas-gray);
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.no-todos {
  text-align: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.no-todos-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-todos h3 {
  color: var(--tunas-primary);
  margin-bottom: 1rem;
}

.no-todos p {
  color: var(--tunas-gray);
  max-width: 400px;
  margin: 0 auto;
  line-height: 1.6;
}

.todo-sections {
  display: grid;
  gap: 2rem;
}

.todo-section h3 {
  color: var(--tunas-primary);
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.todo-list {
  display: grid;
  gap: 1rem;
}

.todo-item {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border-left: 4px solid var(--tunas-secondary);
  transition: all 0.3s ease;
}

.todo-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.todo-item.completed {
  opacity: 0.7;
  border-left-color: var(--tunas-accent);
}

.todo-item.completed .todo-info h4 {
  text-decoration: line-through;
}

.todo-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.todo-main {
  display: flex;
  gap: 1rem;
  flex: 1;
}

.complete-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  transition: transform 0.2s ease;
}

.complete-btn:hover {
  transform: scale(1.2);
}

.todo-info {
  flex: 1;
}

.todo-info h4 {
  margin: 0 0 0.5rem 0;
  color: var(--tunas-primary);
  font-size: 1.1rem;
}

.todo-info p {
  margin: 0 0 1rem 0;
  color: var(--tunas-gray);
  line-height: 1.5;
}

.todo-meta {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.priority {
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
}

.due-date {
  color: var(--tunas-gray);
  font-size: 0.9rem;
}

.delete-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
  opacity: 0.6;
}

.delete-btn:hover {
  opacity: 1;
  background-color: #fef2f2;
  transform: scale(1.1);
}

/* Responsive */
@media (max-width: 768px) {
  .todo-container {
    padding: 1rem 0.5rem;
  }
  
  .todo-header {
    padding: 1.5rem;
  }
  
  .todo-header h2 {
    font-size: 1.5rem;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .todo-content {
    flex-direction: column;
    gap: 1rem;
  }
  
  .todo-main {
    align-items: flex-start;
  }
  
  .todo-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
