{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\metin_kayit\\\\talep-yonetim-frontend\\\\src\\\\components\\\\Navbar.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport './Navbar.css';\n// Logo import - logo dosyasını src/assets/images/logos/ klasörüne koyun\nimport tunasLogo from '../assets/images/logos/tunas-logo-white.png';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Navbar = ({\n  onShowAbout,\n  onNavigate\n}) => {\n  _s();\n  const [darkMode, setDarkMode] = useState(false);\n  const [showProfile, setShowProfile] = useState(false);\n  const {\n    user,\n    logout,\n    isAdmin\n  } = useAuth();\n  const toggleDarkMode = () => {\n    setDarkMode(!darkMode);\n    // Dark mode functionality can be implemented later\n    console.log('Dark mode:', !darkMode);\n  };\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"navbar\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navbar-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"navbar-brand\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo-container\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: tunasLogo,\n            alt: \"T\\xDCNA\\u015E Logo\",\n            className: \"navbar-logo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"navbar-menu\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"navbar-nav\",\n          children: [/*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#dashboard\",\n            className: \"nav-link active\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"icon-dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 15\n            }, this), \"Ana Sayfa\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this), isAdmin() ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => onNavigate && onNavigate('dashboard'),\n              className: \"nav-link\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"icon-requests\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 37,\n                columnNumber: 19\n              }, this), \"T\\xFCm Talepler\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => onNavigate && onNavigate('todo-management'),\n              className: \"nav-link\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"icon-todo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 41,\n                columnNumber: 19\n              }, this), \"Yap\\u0131lacaklar\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => onNavigate && onNavigate('survey-create'),\n              className: \"nav-link\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"icon-survey\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 19\n              }, this), \"Anket Olu\\u015Ftur\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => onNavigate && onNavigate('announcements-create'),\n              className: \"nav-link\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"icon-news\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 49,\n                columnNumber: 19\n              }, this), \"Duyuru/Haber\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => onNavigate && onNavigate('events-create'),\n              className: \"nav-link\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"icon-calendar\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 53,\n                columnNumber: 19\n              }, this), \"Etkinlik Olu\\u015Ftur\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => onNavigate && onNavigate('dashboard'),\n              className: \"nav-link\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"icon-requests\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 60,\n                columnNumber: 19\n              }, this), \"Taleplerim\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => onNavigate && onNavigate('favorite-requests'),\n              className: \"nav-link\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"icon-favorite\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 19\n              }, this), \"Favori Talepler\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => onNavigate && onNavigate('todo-list'),\n              className: \"nav-link\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"icon-todo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 19\n              }, this), \"Yap\\u0131lacaklar\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => onNavigate && onNavigate('surveys'),\n              className: \"nav-link\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"icon-survey\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 19\n              }, this), \"Anketler\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => onNavigate && onNavigate('announcements'),\n              className: \"nav-link\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"icon-news\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 19\n              }, this), \"Duyurular\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => onNavigate && onNavigate('events'),\n              className: \"nav-link\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"icon-calendar\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 80,\n                columnNumber: 19\n              }, this), \"Etkinlik Takvimi\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onShowAbout,\n            className: \"nav-link nav-button\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"icon-info\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 15\n            }, this), \"T\\xDCNA\\u015E Hakk\\u0131nda\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"navbar-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-btn\",\n            onClick: toggleDarkMode,\n            title: \"Gece Modu\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: `icon-${darkMode ? 'sun' : 'moon'}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-btn\",\n            title: \"Ayarlar\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"icon-settings\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"profile-dropdown\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"profile-btn\",\n              onClick: () => setShowProfile(!showProfile),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"profile-avatar\",\n                children: [user.firstName.charAt(0), user.lastName.charAt(0)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"profile-name\",\n                children: [user.firstName, \" \", user.lastName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"icon-chevron-down\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this), showProfile && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"profile-menu\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"profile-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"profile-avatar large\",\n                  children: [user.firstName.charAt(0), user.lastName.charAt(0)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 128,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"profile-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: [user.firstName, \" \", user.lastName]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 132,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: user.employeeNumber\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 133,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: user.department\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 134,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `role-badge ${user.userType.toLowerCase()}`,\n                    children: user.userType === 'Admin' ? 'Yönetici' : 'Kullanıcı'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 135,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"profile-actions\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"profile-action\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"icon-user\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 143,\n                    columnNumber: 23\n                  }, this), \"Profil Bilgilerim\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 142,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"profile-action\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"icon-edit\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 147,\n                    columnNumber: 23\n                  }, this), \"Profil D\\xFCzenle\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"profile-action\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"icon-settings\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 151,\n                    columnNumber: 23\n                  }, this), \"Ayarlar\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"profile-action\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"icon-help\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 155,\n                    columnNumber: 23\n                  }, this), \"Yard\\u0131m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"profile-action logout\",\n                  onClick: logout,\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"icon-logout\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 160,\n                    columnNumber: 23\n                  }, this), \"\\xC7\\u0131k\\u0131\\u015F Yap\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n};\n_s(Navbar, \"eeJgwquWg08JRQoFS4EEfS1rJy0=\", false, function () {\n  return [useAuth];\n});\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "useState", "useAuth", "tunasLogo", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON>", "onShowAbout", "onNavigate", "_s", "darkMode", "setDarkMode", "showProfile", "setShowProfile", "user", "logout", "isAdmin", "toggleDarkMode", "console", "log", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "onClick", "title", "firstName", "char<PERSON>t", "lastName", "employeeNumber", "department", "userType", "toLowerCase", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/metin_kayit/talep-yonetim-frontend/src/components/Navbar.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport './Navbar.css';\n// Logo import - logo dosyasını src/assets/images/logos/ klasörüne koyun\nimport tunasLogo from '../assets/images/logos/tunas-logo-white.png';\n\nconst Navbar = ({ onShowAbout, onNavigate }) => {\n  const [darkMode, setDarkMode] = useState(false);\n  const [showProfile, setShowProfile] = useState(false);\n  const { user, logout, isAdmin } = useAuth();\n\n  const toggleDarkMode = () => {\n    setDarkMode(!darkMode);\n    // Dark mode functionality can be implemented later\n    console.log('Dark mode:', !darkMode);\n  };\n\n  return (\n    <nav className=\"navbar\">\n      <div className=\"navbar-container\">\n        <div className=\"navbar-brand\">\n          <div className=\"logo-container\">\n            <img src={tunasLogo} alt=\"TÜNAŞ Logo\" className=\"navbar-logo\" />\n          </div>\n        </div>\n\n        <div className=\"navbar-menu\">\n          <div className=\"navbar-nav\">\n            <a href=\"#dashboard\" className=\"nav-link active\">\n              <i className=\"icon-dashboard\"></i>\n              Ana Sayfa\n            </a>\n            \n            {isAdmin() ? (\n              <>\n                <button onClick={() => onNavigate && onNavigate('dashboard')} className=\"nav-link\">\n                  <i className=\"icon-requests\"></i>\n                  Tüm Talepler\n                </button>\n                <button onClick={() => onNavigate && onNavigate('todo-management')} className=\"nav-link\">\n                  <i className=\"icon-todo\"></i>\n                  Yapılacaklar\n                </button>\n                <button onClick={() => onNavigate && onNavigate('survey-create')} className=\"nav-link\">\n                  <i className=\"icon-survey\"></i>\n                  Anket Oluştur\n                </button>\n                <button onClick={() => onNavigate && onNavigate('announcements-create')} className=\"nav-link\">\n                  <i className=\"icon-news\"></i>\n                  Duyuru/Haber\n                </button>\n                <button onClick={() => onNavigate && onNavigate('events-create')} className=\"nav-link\">\n                  <i className=\"icon-calendar\"></i>\n                  Etkinlik Oluştur\n                </button>\n              </>\n            ) : (\n              <>\n                <button onClick={() => onNavigate && onNavigate('dashboard')} className=\"nav-link\">\n                  <i className=\"icon-requests\"></i>\n                  Taleplerim\n                </button>\n                <button onClick={() => onNavigate && onNavigate('favorite-requests')} className=\"nav-link\">\n                  <i className=\"icon-favorite\"></i>\n                  Favori Talepler\n                </button>\n                <button onClick={() => onNavigate && onNavigate('todo-list')} className=\"nav-link\">\n                  <i className=\"icon-todo\"></i>\n                  Yapılacaklar\n                </button>\n                <button onClick={() => onNavigate && onNavigate('surveys')} className=\"nav-link\">\n                  <i className=\"icon-survey\"></i>\n                  Anketler\n                </button>\n                <button onClick={() => onNavigate && onNavigate('announcements')} className=\"nav-link\">\n                  <i className=\"icon-news\"></i>\n                  Duyurular\n                </button>\n                <button onClick={() => onNavigate && onNavigate('events')} className=\"nav-link\">\n                  <i className=\"icon-calendar\"></i>\n                  Etkinlik Takvimi\n                </button>\n              </>\n            )}\n            \n            <button\n              onClick={onShowAbout}\n              className=\"nav-link nav-button\"\n            >\n              <i className=\"icon-info\"></i>\n              TÜNAŞ Hakkında\n            </button>\n          </div>\n\n          <div className=\"navbar-actions\">\n            <button \n              className=\"action-btn\"\n              onClick={toggleDarkMode}\n              title=\"Gece Modu\"\n            >\n              <i className={`icon-${darkMode ? 'sun' : 'moon'}`}></i>\n            </button>\n\n            <button \n              className=\"action-btn\"\n              title=\"Ayarlar\"\n            >\n              <i className=\"icon-settings\"></i>\n            </button>\n\n            <div className=\"profile-dropdown\">\n              <button \n                className=\"profile-btn\"\n                onClick={() => setShowProfile(!showProfile)}\n              >\n                <div className=\"profile-avatar\">\n                  {user.firstName.charAt(0)}{user.lastName.charAt(0)}\n                </div>\n                <span className=\"profile-name\">\n                  {user.firstName} {user.lastName}\n                </span>\n                <i className=\"icon-chevron-down\"></i>\n              </button>\n\n              {showProfile && (\n                <div className=\"profile-menu\">\n                  <div className=\"profile-header\">\n                    <div className=\"profile-avatar large\">\n                      {user.firstName.charAt(0)}{user.lastName.charAt(0)}\n                    </div>\n                    <div className=\"profile-info\">\n                      <h4>{user.firstName} {user.lastName}</h4>\n                      <p>{user.employeeNumber}</p>\n                      <p>{user.department}</p>\n                      <span className={`role-badge ${user.userType.toLowerCase()}`}>\n                        {user.userType === 'Admin' ? 'Yönetici' : 'Kullanıcı'}\n                      </span>\n                    </div>\n                  </div>\n                  \n                  <div className=\"profile-actions\">\n                    <button className=\"profile-action\">\n                      <i className=\"icon-user\"></i>\n                      Profil Bilgilerim\n                    </button>\n                    <button className=\"profile-action\">\n                      <i className=\"icon-edit\"></i>\n                      Profil Düzenle\n                    </button>\n                    <button className=\"profile-action\">\n                      <i className=\"icon-settings\"></i>\n                      Ayarlar\n                    </button>\n                    <button className=\"profile-action\">\n                      <i className=\"icon-help\"></i>\n                      Yardım\n                    </button>\n                    <hr />\n                    <button className=\"profile-action logout\" onClick={logout}>\n                      <i className=\"icon-logout\"></i>\n                      Çıkış Yap\n                    </button>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n};\n\nexport default Navbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAO,cAAc;AACrB;AACA,OAAOC,SAAS,MAAM,6CAA6C;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpE,MAAMC,MAAM,GAAGA,CAAC;EAAEC,WAAW;EAAEC;AAAW,CAAC,KAAK;EAAAC,EAAA;EAC9C,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM;IAAEe,IAAI;IAAEC,MAAM;IAAEC;EAAQ,CAAC,GAAGhB,OAAO,CAAC,CAAC;EAE3C,MAAMiB,cAAc,GAAGA,CAAA,KAAM;IAC3BN,WAAW,CAAC,CAACD,QAAQ,CAAC;IACtB;IACAQ,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE,CAACT,QAAQ,CAAC;EACtC,CAAC;EAED,oBACEP,OAAA;IAAKiB,SAAS,EAAC,QAAQ;IAAAC,QAAA,eACrBlB,OAAA;MAAKiB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BlB,OAAA;QAAKiB,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BlB,OAAA;UAAKiB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC7BlB,OAAA;YAAKmB,GAAG,EAAErB,SAAU;YAACsB,GAAG,EAAC,oBAAY;YAACH,SAAS,EAAC;UAAa;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENxB,OAAA;QAAKiB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BlB,OAAA;UAAKiB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBlB,OAAA;YAAGyB,IAAI,EAAC,YAAY;YAACR,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9ClB,OAAA;cAAGiB,SAAS,EAAC;YAAgB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,aAEpC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,EAEHX,OAAO,CAAC,CAAC,gBACRb,OAAA,CAAAE,SAAA;YAAAgB,QAAA,gBACElB,OAAA;cAAQ0B,OAAO,EAAEA,CAAA,KAAMrB,UAAU,IAAIA,UAAU,CAAC,WAAW,CAAE;cAACY,SAAS,EAAC,UAAU;cAAAC,QAAA,gBAChFlB,OAAA;gBAAGiB,SAAS,EAAC;cAAe;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,mBAEnC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTxB,OAAA;cAAQ0B,OAAO,EAAEA,CAAA,KAAMrB,UAAU,IAAIA,UAAU,CAAC,iBAAiB,CAAE;cAACY,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACtFlB,OAAA;gBAAGiB,SAAS,EAAC;cAAW;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,qBAE/B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTxB,OAAA;cAAQ0B,OAAO,EAAEA,CAAA,KAAMrB,UAAU,IAAIA,UAAU,CAAC,eAAe,CAAE;cAACY,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACpFlB,OAAA;gBAAGiB,SAAS,EAAC;cAAa;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,sBAEjC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTxB,OAAA;cAAQ0B,OAAO,EAAEA,CAAA,KAAMrB,UAAU,IAAIA,UAAU,CAAC,sBAAsB,CAAE;cAACY,SAAS,EAAC,UAAU;cAAAC,QAAA,gBAC3FlB,OAAA;gBAAGiB,SAAS,EAAC;cAAW;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,gBAE/B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTxB,OAAA;cAAQ0B,OAAO,EAAEA,CAAA,KAAMrB,UAAU,IAAIA,UAAU,CAAC,eAAe,CAAE;cAACY,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACpFlB,OAAA;gBAAGiB,SAAS,EAAC;cAAe;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,yBAEnC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,eACT,CAAC,gBAEHxB,OAAA,CAAAE,SAAA;YAAAgB,QAAA,gBACElB,OAAA;cAAQ0B,OAAO,EAAEA,CAAA,KAAMrB,UAAU,IAAIA,UAAU,CAAC,WAAW,CAAE;cAACY,SAAS,EAAC,UAAU;cAAAC,QAAA,gBAChFlB,OAAA;gBAAGiB,SAAS,EAAC;cAAe;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,cAEnC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTxB,OAAA;cAAQ0B,OAAO,EAAEA,CAAA,KAAMrB,UAAU,IAAIA,UAAU,CAAC,mBAAmB,CAAE;cAACY,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACxFlB,OAAA;gBAAGiB,SAAS,EAAC;cAAe;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,mBAEnC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTxB,OAAA;cAAQ0B,OAAO,EAAEA,CAAA,KAAMrB,UAAU,IAAIA,UAAU,CAAC,WAAW,CAAE;cAACY,SAAS,EAAC,UAAU;cAAAC,QAAA,gBAChFlB,OAAA;gBAAGiB,SAAS,EAAC;cAAW;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,qBAE/B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTxB,OAAA;cAAQ0B,OAAO,EAAEA,CAAA,KAAMrB,UAAU,IAAIA,UAAU,CAAC,SAAS,CAAE;cAACY,SAAS,EAAC,UAAU;cAAAC,QAAA,gBAC9ElB,OAAA;gBAAGiB,SAAS,EAAC;cAAa;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,YAEjC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTxB,OAAA;cAAQ0B,OAAO,EAAEA,CAAA,KAAMrB,UAAU,IAAIA,UAAU,CAAC,eAAe,CAAE;cAACY,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACpFlB,OAAA;gBAAGiB,SAAS,EAAC;cAAW;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,aAE/B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTxB,OAAA;cAAQ0B,OAAO,EAAEA,CAAA,KAAMrB,UAAU,IAAIA,UAAU,CAAC,QAAQ,CAAE;cAACY,SAAS,EAAC,UAAU;cAAAC,QAAA,gBAC7ElB,OAAA;gBAAGiB,SAAS,EAAC;cAAe;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,oBAEnC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,eACT,CACH,eAEDxB,OAAA;YACE0B,OAAO,EAAEtB,WAAY;YACrBa,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAE/BlB,OAAA;cAAGiB,SAAS,EAAC;YAAW;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,+BAE/B;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENxB,OAAA;UAAKiB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BlB,OAAA;YACEiB,SAAS,EAAC,YAAY;YACtBS,OAAO,EAAEZ,cAAe;YACxBa,KAAK,EAAC,WAAW;YAAAT,QAAA,eAEjBlB,OAAA;cAAGiB,SAAS,EAAE,QAAQV,QAAQ,GAAG,KAAK,GAAG,MAAM;YAAG;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eAETxB,OAAA;YACEiB,SAAS,EAAC,YAAY;YACtBU,KAAK,EAAC,SAAS;YAAAT,QAAA,eAEflB,OAAA;cAAGiB,SAAS,EAAC;YAAe;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eAETxB,OAAA;YAAKiB,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BlB,OAAA;cACEiB,SAAS,EAAC,aAAa;cACvBS,OAAO,EAAEA,CAAA,KAAMhB,cAAc,CAAC,CAACD,WAAW,CAAE;cAAAS,QAAA,gBAE5ClB,OAAA;gBAAKiB,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,GAC5BP,IAAI,CAACiB,SAAS,CAACC,MAAM,CAAC,CAAC,CAAC,EAAElB,IAAI,CAACmB,QAAQ,CAACD,MAAM,CAAC,CAAC,CAAC;cAAA;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eACNxB,OAAA;gBAAMiB,SAAS,EAAC,cAAc;gBAAAC,QAAA,GAC3BP,IAAI,CAACiB,SAAS,EAAC,GAAC,EAACjB,IAAI,CAACmB,QAAQ;cAAA;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eACPxB,OAAA;gBAAGiB,SAAS,EAAC;cAAmB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,EAERf,WAAW,iBACVT,OAAA;cAAKiB,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BlB,OAAA;gBAAKiB,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BlB,OAAA;kBAAKiB,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,GAClCP,IAAI,CAACiB,SAAS,CAACC,MAAM,CAAC,CAAC,CAAC,EAAElB,IAAI,CAACmB,QAAQ,CAACD,MAAM,CAAC,CAAC,CAAC;gBAAA;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC,eACNxB,OAAA;kBAAKiB,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3BlB,OAAA;oBAAAkB,QAAA,GAAKP,IAAI,CAACiB,SAAS,EAAC,GAAC,EAACjB,IAAI,CAACmB,QAAQ;kBAAA;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACzCxB,OAAA;oBAAAkB,QAAA,EAAIP,IAAI,CAACoB;kBAAc;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5BxB,OAAA;oBAAAkB,QAAA,EAAIP,IAAI,CAACqB;kBAAU;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxBxB,OAAA;oBAAMiB,SAAS,EAAE,cAAcN,IAAI,CAACsB,QAAQ,CAACC,WAAW,CAAC,CAAC,EAAG;oBAAAhB,QAAA,EAC1DP,IAAI,CAACsB,QAAQ,KAAK,OAAO,GAAG,UAAU,GAAG;kBAAW;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENxB,OAAA;gBAAKiB,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BlB,OAAA;kBAAQiB,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAChClB,OAAA;oBAAGiB,SAAS,EAAC;kBAAW;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,qBAE/B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTxB,OAAA;kBAAQiB,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAChClB,OAAA;oBAAGiB,SAAS,EAAC;kBAAW;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,qBAE/B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTxB,OAAA;kBAAQiB,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAChClB,OAAA;oBAAGiB,SAAS,EAAC;kBAAe;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,WAEnC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTxB,OAAA;kBAAQiB,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAChClB,OAAA;oBAAGiB,SAAS,EAAC;kBAAW;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAE/B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTxB,OAAA;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNxB,OAAA;kBAAQiB,SAAS,EAAC,uBAAuB;kBAACS,OAAO,EAAEd,MAAO;kBAAAM,QAAA,gBACxDlB,OAAA;oBAAGiB,SAAS,EAAC;kBAAa;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,+BAEjC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClB,EAAA,CArKIH,MAAM;EAAA,QAGwBN,OAAO;AAAA;AAAAsC,EAAA,GAHrChC,MAAM;AAuKZ,eAAeA,MAAM;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}