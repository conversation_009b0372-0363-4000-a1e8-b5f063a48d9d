[{"C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\context\\AuthContext.js": "4", "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\components\\Register.js": "5", "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\components\\UserDashboard.js": "6", "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\components\\Login.js": "7", "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\components\\AdminDashboard.js": "8", "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\components\\CreateRequest.js": "9", "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\services\\api.js": "10", "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\components\\Navbar.js": "11", "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\components\\Footer.js": "12", "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\components\\AboutTunas.js": "13", "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\components\\Surveys.js": "14", "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\components\\FavoriteRequests.js": "15", "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\components\\Announcements.js": "16", "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\components\\EventCalendar.js": "17", "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\components\\TodoList.js": "18", "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\components\\TodoManagement.js": "19", "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\components\\AnnouncementManagement.js": "20", "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\components\\SurveyCreate.js": "21", "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\components\\EventManagement.js": "22"}, {"size": 535, "mtime": 1757263846795, "results": "23", "hashOfConfig": "24"}, {"size": 1034, "mtime": 1757264201219, "results": "25", "hashOfConfig": "24"}, {"size": 362, "mtime": 1757263847434, "results": "26", "hashOfConfig": "24"}, {"size": 1160, "mtime": 1757263925538, "results": "27", "hashOfConfig": "24"}, {"size": 4593, "mtime": 1757266265380, "results": "28", "hashOfConfig": "24"}, {"size": 6103, "mtime": 1757273994822, "results": "29", "hashOfConfig": "24"}, {"size": 2599, "mtime": 1757266218324, "results": "30", "hashOfConfig": "24"}, {"size": 11137, "mtime": 1757274101416, "results": "31", "hashOfConfig": "24"}, {"size": 2609, "mtime": 1757264036430, "results": "32", "hashOfConfig": "24"}, {"size": 3750, "mtime": 1757269576241, "results": "33", "hashOfConfig": "24"}, {"size": 4115, "mtime": 1757273887671, "results": "34", "hashOfConfig": "24"}, {"size": 5928, "mtime": 1757266464216, "results": "35", "hashOfConfig": "24"}, {"size": 5714, "mtime": 1757265851977, "results": "36", "hashOfConfig": "24"}, {"size": 9533, "mtime": 1757269794318, "results": "37", "hashOfConfig": "24"}, {"size": 4586, "mtime": 1757269604261, "results": "38", "hashOfConfig": "24"}, {"size": 6782, "mtime": 1757269900002, "results": "39", "hashOfConfig": "24"}, {"size": 11513, "mtime": 1757270066403, "results": "40", "hashOfConfig": "24"}, {"size": 10070, "mtime": 1757269671957, "results": "41", "hashOfConfig": "24"}, {"size": 6886, "mtime": 1757271167624, "results": "42", "hashOfConfig": "24"}, {"size": 10176, "mtime": 1757271360132, "results": "43", "hashOfConfig": "24"}, {"size": 10822, "mtime": 1757271239260, "results": "44", "hashOfConfig": "24"}, {"size": 11770, "mtime": 1757271465301, "results": "45", "hashOfConfig": "24"}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1djjx0l", {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\context\\AuthContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\components\\Register.js", [], [], "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\components\\UserDashboard.js", ["112"], ["113"], "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\components\\Login.js", [], [], "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\components\\AdminDashboard.js", [], [], "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\components\\CreateRequest.js", [], [], "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\components\\Navbar.js", ["114"], [], "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\components\\Footer.js", [], [], "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\components\\AboutTunas.js", [], [], "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\components\\Surveys.js", ["115"], [], "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\components\\FavoriteRequests.js", ["116"], [], "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\components\\Announcements.js", [], [], "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\components\\EventCalendar.js", ["117"], [], "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\components\\TodoList.js", ["118"], [], "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\components\\TodoManagement.js", [], [], "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\components\\AnnouncementManagement.js", [], [], "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\components\\SurveyCreate.js", [], [], "C:\\Users\\<USER>\\Desktop\\metin_kayit\\talep-yonetim-frontend\\src\\components\\EventManagement.js", [], [], {"ruleId": "119", "severity": 1, "message": "120", "line": 50, "column": 9, "nodeType": "121", "messageId": "122", "endLine": 50, "endColumn": 25}, {"ruleId": "123", "severity": 1, "message": "124", "line": 26, "column": 6, "nodeType": "125", "endLine": 26, "endColumn": 8, "suggestions": "126", "suppressions": "127"}, {"ruleId": "119", "severity": 1, "message": "128", "line": 10, "column": 25, "nodeType": "121", "messageId": "122", "endLine": 10, "endColumn": 32}, {"ruleId": "123", "severity": 1, "message": "129", "line": 19, "column": 6, "nodeType": "125", "endLine": 19, "endColumn": 8, "suggestions": "130"}, {"ruleId": "123", "severity": 1, "message": "131", "line": 14, "column": 6, "nodeType": "125", "endLine": 14, "endColumn": 8, "suggestions": "132"}, {"ruleId": "123", "severity": 1, "message": "133", "line": 15, "column": 6, "nodeType": "125", "endLine": 15, "endColumn": 19, "suggestions": "134"}, {"ruleId": "123", "severity": 1, "message": "135", "line": 21, "column": 6, "nodeType": "125", "endLine": 21, "endColumn": 8, "suggestions": "136"}, "no-unused-vars", "'handleNavigation' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchUserRequests'. Either include it or remove the dependency array.", "ArrayExpression", ["137"], ["138"], "'isAdmin' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAnsweredSurveys'. Either include it or remove the dependency array.", ["139"], "React Hook useEffect has a missing dependency: 'fetchFavorites'. Either include it or remove the dependency array.", ["140"], "React Hook useEffect has a missing dependency: 'fetchEvents'. Either include it or remove the dependency array.", ["141"], "React Hook useEffect has a missing dependency: 'fetchTodos'. Either include it or remove the dependency array.", ["142"], {"desc": "143", "fix": "144"}, {"kind": "145", "justification": "146"}, {"desc": "147", "fix": "148"}, {"desc": "149", "fix": "150"}, {"desc": "151", "fix": "152"}, {"desc": "153", "fix": "154"}, "Update the dependencies array to be: [fetchUserRequests]", {"range": "155", "text": "156"}, "directive", "", "Update the dependencies array to be: [fetchAnsweredSurveys]", {"range": "157", "text": "158"}, "Update the dependencies array to be: [fetchFavorites]", {"range": "159", "text": "160"}, "Update the dependencies array to be: [currentDate, fetchEvents]", {"range": "161", "text": "162"}, "Update the dependencies array to be: [fetchTodos]", {"range": "163", "text": "164"}, [988, 990], "[fetchUserRequests]", [705, 707], "[fetchAnsweredSurveys]", [435, 437], "[fetchFavorites]", [533, 546], "[currentDate, fetchEvents]", [584, 586], "[fetchTodos]"]