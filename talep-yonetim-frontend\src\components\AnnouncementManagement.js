import React, { useState, useEffect } from 'react';
import { announcementsAPI } from '../services/api';
import { useAuth } from '../context/AuthContext';
import './AnnouncementManagement.css';

const AnnouncementManagement = () => {
  const [announcements, setAnnouncements] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [filterType, setFilterType] = useState('all'); // 'all', 'Announcement', 'News'
  const [newItem, setNewItem] = useState({
    title: '',
    content: '',
    type: 'Announcement',
    isImportant: false
  });
  const { user } = useAuth();

  useEffect(() => {
    fetchAnnouncements();
  }, []);

  const fetchAnnouncements = async () => {
    try {
      setLoading(true);
      const response = await announcementsAPI.getAll();
      setAnnouncements(response.data);
    } catch (error) {
      setError('Duyurular yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = async (e) => {
    e.preventDefault();
    try {
      await announcementsAPI.create(newItem, user.id);
      setSuccess(`${newItem.type === 'Announcement' ? 'Duyuru' : 'Haber'} başarıyla oluşturuldu!`);
      setNewItem({
        title: '',
        content: '',
        type: 'Announcement',
        isImportant: false
      });
      setShowCreateForm(false);
      fetchAnnouncements();
      setTimeout(() => setSuccess(''), 3000);
    } catch (error) {
      setError('Oluşturulurken bir hata oluştu.');
    }
  };

  const handleUpdate = async (e) => {
    e.preventDefault();
    try {
      await announcementsAPI.update(editingItem.id, newItem, user.id);
      setSuccess(`${newItem.type === 'Announcement' ? 'Duyuru' : 'Haber'} başarıyla güncellendi!`);
      setEditingItem(null);
      setNewItem({
        title: '',
        content: '',
        type: 'Announcement',
        isImportant: false
      });
      fetchAnnouncements();
      setTimeout(() => setSuccess(''), 3000);
    } catch (error) {
      setError('Güncellenirken bir hata oluştu.');
    }
  };

  const handleDelete = async (id, type) => {
    if (!window.confirm(`Bu ${type === 'Announcement' ? 'duyuru' : 'haber'}yu silmek istediğinizden emin misiniz?`)) {
      return;
    }
    
    try {
      await announcementsAPI.delete(id, user.id);
      setSuccess(`${type === 'Announcement' ? 'Duyuru' : 'Haber'} başarıyla silindi!`);
      fetchAnnouncements();
      setTimeout(() => setSuccess(''), 3000);
    } catch (error) {
      setError('Silinirken bir hata oluştu.');
    }
  };

  const startEdit = (item) => {
    setEditingItem(item);
    setNewItem({
      title: item.title,
      content: item.content,
      type: item.type,
      isImportant: item.isImportant
    });
    setShowCreateForm(true);
  };

  const cancelEdit = () => {
    setEditingItem(null);
    setNewItem({
      title: '',
      content: '',
      type: 'Announcement',
      isImportant: false
    });
    setShowCreateForm(false);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const filteredItems = announcements.filter(item => 
    filterType === 'all' || item.type === filterType
  );

  const announcementCount = announcements.filter(item => item.type === 'Announcement').length;
  const newsCount = announcements.filter(item => item.type === 'News').length;
  const importantCount = announcements.filter(item => item.isImportant).length;

  if (loading) {
    return (
      <div className="announcement-management-container">
        <div className="loading">Duyurular yükleniyor...</div>
      </div>
    );
  }

  return (
    <div className="announcement-management-container">
      <div className="announcement-management-header">
        <h2>📰 Duyuru & Haber Yönetimi</h2>
        <p>Duyuru ve haberleri oluşturun, düzenleyin ve yönetin.</p>
        <button 
          onClick={() => setShowCreateForm(!showCreateForm)}
          className="create-announcement-btn"
        >
          ➕ Yeni {editingItem ? 'Düzenle' : 'Oluştur'}
        </button>
      </div>

      {error && <div className="error-message">{error}</div>}
      {success && <div className="success-message">{success}</div>}

      <div className="stats-section">
        <div className="stat-card">
          <div className="stat-number">{announcements.length}</div>
          <div className="stat-label">Toplam</div>
        </div>
        <div className="stat-card">
          <div className="stat-number">{announcementCount}</div>
          <div className="stat-label">Duyuru</div>
        </div>
        <div className="stat-card">
          <div className="stat-number">{newsCount}</div>
          <div className="stat-label">Haber</div>
        </div>
        <div className="stat-card important">
          <div className="stat-number">{importantCount}</div>
          <div className="stat-label">Önemli</div>
        </div>
      </div>

      {showCreateForm && (
        <div className="create-form">
          <h3>{editingItem ? 'Düzenle' : 'Yeni Oluştur'}</h3>
          <form onSubmit={editingItem ? handleUpdate : handleCreate}>
            <div className="form-row">
              <div className="form-group">
                <label>Tür *</label>
                <select
                  value={newItem.type}
                  onChange={(e) => setNewItem({...newItem, type: e.target.value})}
                  required
                >
                  <option value="Announcement">📢 Duyuru</option>
                  <option value="News">📰 Haber</option>
                </select>
              </div>
              
              <div className="form-group">
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={newItem.isImportant}
                    onChange={(e) => setNewItem({...newItem, isImportant: e.target.checked})}
                  />
                  ⚠️ Önemli olarak işaretle
                </label>
              </div>
            </div>
            
            <div className="form-group">
              <label>Başlık *</label>
              <input
                type="text"
                value={newItem.title}
                onChange={(e) => setNewItem({...newItem, title: e.target.value})}
                placeholder="Başlık girin..."
                required
              />
            </div>
            
            <div className="form-group">
              <label>İçerik *</label>
              <textarea
                value={newItem.content}
                onChange={(e) => setNewItem({...newItem, content: e.target.value})}
                placeholder="İçerik yazın..."
                rows="8"
                required
              />
            </div>
            
            <div className="form-actions">
              <button type="submit" className="submit-btn">
                {editingItem ? 'Güncelle' : 'Oluştur'}
              </button>
              <button 
                type="button" 
                onClick={cancelEdit}
                className="cancel-btn"
              >
                İptal
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="filter-section">
        <div className="filter-group">
          <label>Filtrele:</label>
          <select 
            value={filterType} 
            onChange={(e) => setFilterType(e.target.value)}
            className="filter-select"
          >
            <option value="all">Tümü ({announcements.length})</option>
            <option value="Announcement">📢 Duyurular ({announcementCount})</option>
            <option value="News">📰 Haberler ({newsCount})</option>
          </select>
        </div>
      </div>

      {filteredItems.length === 0 ? (
        <div className="no-items">
          <div className="no-items-icon">📰</div>
          <h3>İçerik bulunamadı</h3>
          <p>Seçili filtreye uygun içerik bulunmuyor.</p>
        </div>
      ) : (
        <div className="items-list">
          {filteredItems.map(item => (
            <div key={item.id} className={`item-card ${item.isImportant ? 'important' : ''}`}>
              <div className="item-header">
                <div className="item-type">
                  {item.type === 'Announcement' ? '📢 Duyuru' : '📰 Haber'}
                  {item.isImportant && <span className="important-badge">⚠️ Önemli</span>}
                </div>
                <div className="item-actions">
                  <button 
                    onClick={() => startEdit(item)}
                    className="edit-btn"
                    title="Düzenle"
                  >
                    ✏️
                  </button>
                  <button 
                    onClick={() => handleDelete(item.id, item.type)}
                    className="delete-btn"
                    title="Sil"
                  >
                    🗑️
                  </button>
                </div>
              </div>
              
              <div className="item-content">
                <h4>{item.title}</h4>
                <p>{item.content.substring(0, 200)}...</p>
              </div>
              
              <div className="item-meta">
                <span className="created-date">📅 {formatDate(item.createdAt)}</span>
                {item.updatedAt !== item.createdAt && (
                  <span className="updated-date">✏️ Güncellendi: {formatDate(item.updatedAt)}</span>
                )}
                <span className="admin-name">👤 {item.adminName}</span>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default AnnouncementManagement;
