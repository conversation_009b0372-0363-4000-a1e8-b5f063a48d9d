using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TalepYonetimAPI.Data;
using TalepYonetimAPI.Models;

namespace TalepYonetimAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class FavoriteRequestsController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<FavoriteRequestsController> _logger;

        public FavoriteRequestsController(ApplicationDbContext context, ILogger<FavoriteRequestsController> logger)
        {
            _context = context;
            _logger = logger;
        }

        [HttpPost]
        public async Task<IActionResult> AddToFavorites([FromQuery] int userId, [FromQuery] int requestId)
        {
            try
            {
                // Kullanıcı ve talep var mı kontrol et
                var user = await _context.Users.FindAsync(userId);
                var request = await _context.Requests.FindAsync(requestId);

                if (user == null || request == null)
                {
                    return BadRequest(new { message = "<PERSON>llanıcı veya talep bulunamadı." });
                }

                // Zaten favorilerde mi kontrol et
                var existingFavorite = await _context.FavoriteRequests
                    .FirstOrDefaultAsync(f => f.UserId == userId && f.RequestId == requestId);

                if (existingFavorite != null)
                {
                    return BadRequest(new { message = "Bu talep zaten favorilerinizde." });
                }

                var favoriteRequest = new FavoriteRequest
                {
                    UserId = userId,
                    RequestId = requestId
                };

                _context.FavoriteRequests.Add(favoriteRequest);
                await _context.SaveChangesAsync();

                return Ok(new { message = "Talep favorilere eklendi." });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while adding to favorites");
                return StatusCode(500, new { message = "Favorilere eklenirken bir hata oluştu." });
            }
        }

        [HttpDelete]
        public async Task<IActionResult> RemoveFromFavorites([FromQuery] int userId, [FromQuery] int requestId)
        {
            try
            {
                var favoriteRequest = await _context.FavoriteRequests
                    .FirstOrDefaultAsync(f => f.UserId == userId && f.RequestId == requestId);

                if (favoriteRequest == null)
                {
                    return NotFound(new { message = "Favori talep bulunamadı." });
                }

                _context.FavoriteRequests.Remove(favoriteRequest);
                await _context.SaveChangesAsync();

                return Ok(new { message = "Talep favorilerden kaldırıldı." });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while removing from favorites");
                return StatusCode(500, new { message = "Favorilerden kaldırılırken bir hata oluştu." });
            }
        }

        [HttpGet("user/{userId}")]
        public async Task<IActionResult> GetUserFavorites(int userId)
        {
            try
            {
                var favorites = await _context.FavoriteRequests
                    .Where(f => f.UserId == userId)
                    .Include(f => f.Request)
                        .ThenInclude(r => r.Responses)
                            .ThenInclude(resp => resp.Admin)
                    .OrderByDescending(f => f.CreatedAt)
                    .Select(f => new
                    {
                        favoriteId = f.Id,
                        addedAt = f.CreatedAt,
                        request = new
                        {
                            id = f.Request.Id,
                            title = f.Request.Title,
                            content = f.Request.Content,
                            summary = f.Request.Summary,
                            status = f.Request.Status,
                            createdAt = f.Request.CreatedAt,
                            responses = f.Request.Responses.Select(resp => new
                            {
                                id = resp.Id,
                                content = resp.Content,
                                createdAt = resp.CreatedAt,
                                adminName = $"{resp.Admin.FirstName} {resp.Admin.LastName}"
                            }).ToList()
                        }
                    })
                    .ToListAsync();

                return Ok(favorites);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting user favorites");
                return StatusCode(500, new { message = "Favori talepler getirilirken bir hata oluştu." });
            }
        }

        [HttpGet("check")]
        public async Task<IActionResult> CheckIfFavorite([FromQuery] int userId, [FromQuery] int requestId)
        {
            try
            {
                var isFavorite = await _context.FavoriteRequests
                    .AnyAsync(f => f.UserId == userId && f.RequestId == requestId);

                return Ok(new { isFavorite });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while checking favorite status");
                return StatusCode(500, new { message = "Favori durumu kontrol edilirken bir hata oluştu." });
            }
        }
    }
}
