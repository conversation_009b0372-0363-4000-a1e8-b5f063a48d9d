import React, { useState, useEffect } from 'react';
import { todoAPI } from '../services/api';
import './TodoManagement.css';

const TodoManagement = () => {
  const [todos, setTodos] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [filterStatus, setFilterStatus] = useState('all'); // 'all', 'completed', 'pending'
  const [filterPriority, setFilterPriority] = useState('all'); // 'all', 'Low', 'Normal', 'High', 'Urgent'

  useEffect(() => {
    fetchAllTodos();
  }, []);

  const fetchAllTodos = async () => {
    try {
      setLoading(true);
      const response = await todoAPI.getAllForAdmin();
      setTodos(response.data);
    } catch (error) {
      setError('Yapılacaklar yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('tr-TR');
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'Urgent': return '#dc2626';
      case 'High': return '#ea580c';
      case 'Normal': return '#2563eb';
      case 'Low': return '#16a34a';
      default: return '#2563eb';
    }
  };

  const getPriorityText = (priority) => {
    switch (priority) {
      case 'Urgent': return 'Acil';
      case 'High': return 'Yüksek';
      case 'Normal': return 'Normal';
      case 'Low': return 'Düşük';
      default: return 'Normal';
    }
  };

  // Filtreleme
  const filteredTodos = todos.filter(todo => {
    const statusMatch = filterStatus === 'all' || 
                       (filterStatus === 'completed' && todo.isCompleted) ||
                       (filterStatus === 'pending' && !todo.isCompleted);
    
    const priorityMatch = filterPriority === 'all' || todo.priority === filterPriority;
    
    return statusMatch && priorityMatch;
  });

  const completedCount = todos.filter(todo => todo.isCompleted).length;
  const pendingCount = todos.filter(todo => !todo.isCompleted).length;
  const urgentCount = todos.filter(todo => todo.priority === 'Urgent' && !todo.isCompleted).length;

  if (loading) {
    return (
      <div className="todo-management-container">
        <div className="loading">Yapılacaklar yükleniyor...</div>
      </div>
    );
  }

  return (
    <div className="todo-management-container">
      <div className="todo-management-header">
        <h2>📋 Yapılacaklar Yönetimi</h2>
        <p>Tüm kullanıcıların yapılacaklarını görüntüleyin ve takip edin.</p>
      </div>

      {error && <div className="error-message">{error}</div>}

      <div className="todo-stats">
        <div className="stat-card">
          <div className="stat-number">{todos.length}</div>
          <div className="stat-label">Toplam Görev</div>
        </div>
        <div className="stat-card">
          <div className="stat-number">{pendingCount}</div>
          <div className="stat-label">Bekleyen</div>
        </div>
        <div className="stat-card">
          <div className="stat-number">{completedCount}</div>
          <div className="stat-label">Tamamlanan</div>
        </div>
        <div className="stat-card urgent">
          <div className="stat-number">{urgentCount}</div>
          <div className="stat-label">Acil Görevler</div>
        </div>
      </div>

      <div className="filters-section">
        <div className="filter-group">
          <label>Durum:</label>
          <select 
            value={filterStatus} 
            onChange={(e) => setFilterStatus(e.target.value)}
            className="filter-select"
          >
            <option value="all">Tümü</option>
            <option value="pending">Bekleyen</option>
            <option value="completed">Tamamlanan</option>
          </select>
        </div>

        <div className="filter-group">
          <label>Öncelik:</label>
          <select 
            value={filterPriority} 
            onChange={(e) => setFilterPriority(e.target.value)}
            className="filter-select"
          >
            <option value="all">Tümü</option>
            <option value="Urgent">Acil</option>
            <option value="High">Yüksek</option>
            <option value="Normal">Normal</option>
            <option value="Low">Düşük</option>
          </select>
        </div>

        <div className="filter-info">
          <span>{filteredTodos.length} görev gösteriliyor</span>
        </div>
      </div>

      {filteredTodos.length === 0 ? (
        <div className="no-todos">
          <div className="no-todos-icon">📋</div>
          <h3>Görev bulunamadı</h3>
          <p>Seçili filtrelere uygun görev bulunmuyor.</p>
        </div>
      ) : (
        <div className="todos-list">
          {filteredTodos.map(todo => (
            <div key={todo.id} className={`todo-card ${todo.isCompleted ? 'completed' : ''}`}>
              <div className="todo-header">
                <div className="todo-status">
                  {todo.isCompleted ? '✅' : '⭕'}
                </div>
                <div className="todo-info">
                  <h3>{todo.title}</h3>
                  <div className="todo-meta">
                    <span className="user-info">
                      👤 {todo.user.name} ({todo.user.employeeNumber})
                    </span>
                    <span className="department">
                      🏢 {todo.user.department}
                    </span>
                  </div>
                </div>
                <div className="todo-priority">
                  <span 
                    className="priority-badge"
                    style={{ backgroundColor: getPriorityColor(todo.priority) }}
                  >
                    {getPriorityText(todo.priority)}
                  </span>
                </div>
              </div>

              {todo.description && (
                <div className="todo-description">
                  <p>{todo.description}</p>
                </div>
              )}

              <div className="todo-dates">
                <div className="date-item">
                  <span className="date-label">📅 Oluşturulma:</span>
                  <span>{formatDate(todo.createdAt)}</span>
                </div>
                {todo.dueDate && (
                  <div className="date-item">
                    <span className="date-label">⏰ Bitiş:</span>
                    <span className={new Date(todo.dueDate) < new Date() && !todo.isCompleted ? 'overdue' : ''}>
                      {formatDate(todo.dueDate)}
                      {new Date(todo.dueDate) < new Date() && !todo.isCompleted && ' (Gecikmiş)'}
                    </span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default TodoManagement;
