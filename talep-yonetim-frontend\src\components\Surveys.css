.surveys-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.surveys-header {
  text-align: center;
  margin-bottom: 2rem;
  padding: 2rem;
  background: linear-gradient(135deg, #fef7ff 0%, #f3e8ff 100%);
  border-radius: 16px;
  border-left: 5px solid #8b5cf6;
}

.surveys-header h2 {
  margin: 0 0 0.5rem 0;
  color: var(--tunas-primary);
  font-size: 2rem;
}

.surveys-header p {
  margin: 0;
  color: var(--tunas-gray);
  font-size: 1.1rem;
}

.loading {
  text-align: center;
  padding: 3rem;
  font-size: 1.2rem;
  color: var(--tunas-gray);
}

.error-message {
  background-color: #fef2f2;
  color: #dc2626;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  border-left: 4px solid #dc2626;
}

.survey-tabs {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  border-bottom: 2px solid #e5e7eb;
}

.tab-btn {
  background: none;
  border: none;
  padding: 1rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
  color: var(--tunas-gray);
}

.tab-btn.active {
  color: var(--tunas-primary);
  border-bottom-color: var(--tunas-secondary);
}

.tab-btn:hover {
  color: var(--tunas-primary);
  background-color: #f8fafc;
}

.no-surveys {
  text-align: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.no-surveys-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-surveys h3 {
  color: var(--tunas-primary);
  margin-bottom: 1rem;
}

.no-surveys p {
  color: var(--tunas-gray);
  max-width: 400px;
  margin: 0 auto;
  line-height: 1.6;
}

.surveys-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
}

.survey-card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border-left: 5px solid #8b5cf6;
  transition: all 0.3s ease;
}

.survey-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.survey-card.answered {
  border-left-color: var(--tunas-accent);
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
}

.survey-header h3 {
  margin: 0 0 0.5rem 0;
  color: var(--tunas-primary);
  font-size: 1.3rem;
}

.survey-header p {
  margin: 0 0 1rem 0;
  color: var(--tunas-gray);
  line-height: 1.5;
}

.survey-meta {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.admin-name,
.created-date,
.answered-date,
.expiry-date {
  color: var(--tunas-gray);
  font-size: 0.9rem;
}

.expiry-date.expired {
  color: #dc2626;
  font-weight: 500;
}

.survey-stats {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 8px;
}

.response-count {
  color: var(--tunas-primary);
  font-weight: 500;
}

.survey-actions {
  display: flex;
  justify-content: flex-end;
}

.answer-btn {
  background: #8b5cf6;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.answer-btn:hover {
  background: #7c3aed;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.expired-btn {
  background: #9ca3af;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-weight: 500;
  cursor: not-allowed;
}

.answers-preview {
  margin-top: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
}

.answer-item {
  margin-bottom: 1rem;
}

.answer-item:last-child {
  margin-bottom: 0;
}

.answer-item strong {
  color: var(--tunas-primary);
  display: block;
  margin-bottom: 0.25rem;
  font-size: 0.9rem;
}

.answer-item .answer {
  color: var(--tunas-dark);
  font-style: italic;
  padding-left: 1rem;
  border-left: 2px solid var(--tunas-accent);
  margin-top: 0.5rem;
}

.survey-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e5e7eb;
}

.modal-header h3 {
  margin: 0;
  color: var(--tunas-primary);
  font-size: 1.5rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
  color: var(--tunas-gray);
}

.close-btn:hover {
  background-color: #f3f4f6;
  color: var(--tunas-primary);
}

.survey-description {
  margin-bottom: 2rem;
  padding: 1rem;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 8px;
  border-left: 3px solid var(--tunas-secondary);
}

.survey-form {
  display: grid;
  gap: 1.5rem;
}

.question-group {
  display: grid;
  gap: 0.5rem;
}

.question-group label {
  color: var(--tunas-primary);
  font-weight: 500;
  font-size: 1.1rem;
}

.question-group textarea {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  font-family: inherit;
  resize: vertical;
  transition: border-color 0.3s ease;
}

.question-group textarea:focus {
  outline: none;
  border-color: #8b5cf6;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1rem;
}

.submit-btn {
  background: #8b5cf6;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.submit-btn:hover {
  background: #7c3aed;
}

.cancel-btn {
  background: #6b7280;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  background: #4b5563;
}

/* Responsive */
@media (max-width: 768px) {
  .surveys-container {
    padding: 1rem 0.5rem;
  }
  
  .surveys-header {
    padding: 1.5rem;
  }
  
  .surveys-header h2 {
    font-size: 1.5rem;
  }
  
  .surveys-grid {
    grid-template-columns: 1fr;
  }
  
  .survey-tabs {
    flex-direction: column;
  }
  
  .tab-btn {
    text-align: left;
  }
  
  .survey-meta {
    gap: 0.25rem;
  }
  
  .modal-content {
    margin: 1rem;
    padding: 1.5rem;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
