using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TalepYonetimAPI.Data;
using TalepYonetimAPI.Models;
using TalepYonetimAPI.Models.DTOs;

namespace TalepYonetimAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class SurveysController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<SurveysController> _logger;

        public SurveysController(ApplicationDbContext context, ILogger<SurveysController> logger)
        {
            _context = context;
            _logger = logger;
        }

        [HttpPost]
        public async Task<IActionResult> CreateSurvey([FromBody] CreateSurveyDto createSurveyDto, [FromQuery] int adminId)
        {
            try
            {
                var admin = await _context.Users.FindAsync(adminId);
                if (admin == null || admin.UserType != "Admin")
                {
                    return BadRequest(new { message = "Geçersiz admin kullanıcısı." });
                }

                var survey = new Survey
                {
                    AdminId = adminId,
                    Title = createSurveyDto.Title,
                    Description = createSurveyDto.Description,
                    Question1 = createSurveyDto.Question1,
                    Question2 = createSurveyDto.Question2,
                    Question3 = createSurveyDto.Question3,
                    ExpiryDate = createSurveyDto.ExpiryDate
                };

                _context.Surveys.Add(survey);
                await _context.SaveChangesAsync();

                return Ok(new { message = "Anket başarıyla oluşturuldu.", surveyId = survey.Id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while creating survey");
                return StatusCode(500, new { message = "Anket oluşturulurken bir hata oluştu." });
            }
        }

        [HttpGet("active")]
        public async Task<IActionResult> GetActiveSurveys()
        {
            try
            {
                var surveys = await _context.Surveys
                    .Where(s => s.IsActive && (s.ExpiryDate == null || s.ExpiryDate > DateTime.Now))
                    .Include(s => s.Admin)
                    .OrderByDescending(s => s.CreatedAt)
                    .Select(s => new
                    {
                        id = s.Id,
                        title = s.Title,
                        description = s.Description,
                        question1 = s.Question1,
                        question2 = s.Question2,
                        question3 = s.Question3,
                        createdAt = s.CreatedAt,
                        expiryDate = s.ExpiryDate,
                        adminName = $"{s.Admin.FirstName} {s.Admin.LastName}",
                        responseCount = s.SurveyResponses.Count
                    })
                    .ToListAsync();

                return Ok(surveys);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting active surveys");
                return StatusCode(500, new { message = "Aktif anketler getirilirken bir hata oluştu." });
            }
        }

        [HttpPost("respond")]
        public async Task<IActionResult> RespondToSurvey([FromBody] CreateSurveyResponseDto responseDto, [FromQuery] int userId)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    return BadRequest(new { message = "Kullanıcı bulunamadı." });
                }

                var survey = await _context.Surveys.FindAsync(responseDto.SurveyId);
                if (survey == null || !survey.IsActive)
                {
                    return BadRequest(new { message = "Anket bulunamadı veya aktif değil." });
                }

                if (survey.ExpiryDate.HasValue && survey.ExpiryDate < DateTime.Now)
                {
                    return BadRequest(new { message = "Anketin süresi dolmuş." });
                }

                // Kullanıcı daha önce bu anketi cevaplamış mı?
                var existingResponse = await _context.SurveyResponses
                    .FirstOrDefaultAsync(sr => sr.SurveyId == responseDto.SurveyId && sr.UserId == userId);

                if (existingResponse != null)
                {
                    return BadRequest(new { message = "Bu anketi zaten cevaplamışsınız." });
                }

                var surveyResponse = new SurveyResponse
                {
                    SurveyId = responseDto.SurveyId,
                    UserId = userId,
                    Answer1 = responseDto.Answer1,
                    Answer2 = responseDto.Answer2,
                    Answer3 = responseDto.Answer3
                };

                _context.SurveyResponses.Add(surveyResponse);
                await _context.SaveChangesAsync();

                return Ok(new { message = "Anket cevabınız başarıyla kaydedildi." });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while responding to survey");
                return StatusCode(500, new { message = "Anket cevaplanırken bir hata oluştu." });
            }
        }

        [HttpGet("{surveyId}/responses")]
        public async Task<IActionResult> GetSurveyResponses(int surveyId, [FromQuery] int adminId)
        {
            try
            {
                var admin = await _context.Users.FindAsync(adminId);
                if (admin == null || admin.UserType != "Admin")
                {
                    return BadRequest(new { message = "Geçersiz admin kullanıcısı." });
                }

                var responses = await _context.SurveyResponses
                    .Where(sr => sr.SurveyId == surveyId)
                    .Include(sr => sr.User)
                    .Include(sr => sr.Survey)
                    .OrderByDescending(sr => sr.CreatedAt)
                    .Select(sr => new
                    {
                        id = sr.Id,
                        answer1 = sr.Answer1,
                        answer2 = sr.Answer2,
                        answer3 = sr.Answer3,
                        createdAt = sr.CreatedAt,
                        user = new
                        {
                            id = sr.User.Id,
                            name = $"{sr.User.FirstName} {sr.User.LastName}",
                            employeeNumber = sr.User.EmployeeNumber,
                            department = sr.User.Department
                        },
                        survey = new
                        {
                            id = sr.Survey.Id,
                            title = sr.Survey.Title,
                            question1 = sr.Survey.Question1,
                            question2 = sr.Survey.Question2,
                            question3 = sr.Survey.Question3
                        }
                    })
                    .ToListAsync();

                return Ok(responses);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting survey responses");
                return StatusCode(500, new { message = "Anket cevapları getirilirken bir hata oluştu." });
            }
        }

        [HttpGet("user/{userId}/answered")]
        public async Task<IActionResult> GetUserAnsweredSurveys(int userId)
        {
            try
            {
                var answeredSurveys = await _context.SurveyResponses
                    .Where(sr => sr.UserId == userId)
                    .Include(sr => sr.Survey)
                        .ThenInclude(s => s.Admin)
                    .OrderByDescending(sr => sr.CreatedAt)
                    .Select(sr => new
                    {
                        responseId = sr.Id,
                        answeredAt = sr.CreatedAt,
                        answer1 = sr.Answer1,
                        answer2 = sr.Answer2,
                        answer3 = sr.Answer3,
                        survey = new
                        {
                            id = sr.Survey.Id,
                            title = sr.Survey.Title,
                            description = sr.Survey.Description,
                            question1 = sr.Survey.Question1,
                            question2 = sr.Survey.Question2,
                            question3 = sr.Survey.Question3,
                            createdAt = sr.Survey.CreatedAt,
                            adminName = $"{sr.Survey.Admin.FirstName} {sr.Survey.Admin.LastName}"
                        }
                    })
                    .ToListAsync();

                return Ok(answeredSurveys);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting user answered surveys");
                return StatusCode(500, new { message = "Cevaplanan anketler getirilirken bir hata oluştu." });
            }
        }

        [HttpGet("check-answered")]
        public async Task<IActionResult> CheckIfUserAnswered([FromQuery] int userId, [FromQuery] int surveyId)
        {
            try
            {
                var hasAnswered = await _context.SurveyResponses
                    .AnyAsync(sr => sr.UserId == userId && sr.SurveyId == surveyId);

                return Ok(new { hasAnswered });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while checking if user answered survey");
                return StatusCode(500, new { message = "Anket cevap durumu kontrol edilirken bir hata oluştu." });
            }
        }
    }
}
