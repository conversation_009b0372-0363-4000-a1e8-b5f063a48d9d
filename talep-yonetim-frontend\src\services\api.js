import axios from 'axios';

const API_BASE_URL = 'http://localhost:5199/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Auth API calls
export const authAPI = {
  login: (employeeNumber, password) =>
    api.post('/auth/login', { employeeNumber, password }),
  
  register: (userData) =>
    api.post('/auth/register', userData),
};

// Requests API calls
export const requestsAPI = {
  create: (requestData, userId) =>
    api.post(`/requests?userId=${userId}`, requestData),
  
  getUserRequests: (userId) =>
    api.get(`/requests/user/${userId}`),
  
  getAllForAdmin: () =>
    api.get('/requests/admin'),
};

// Responses API calls
export const responsesAPI = {
  create: (responseData, adminId) =>
    api.post(`/responses?adminId=${adminId}`, responseData),

  getByRequest: (requestId) =>
    api.get(`/responses/request/${requestId}`),
};

// Favori Talepler API
export const favoritesAPI = {
  add: (userId, requestId) => api.post(`/favoriterequests?userId=${userId}&requestId=${requestId}`),
  remove: (userId, requestId) => api.delete(`/favoriterequests?userId=${userId}&requestId=${requestId}`),
  getUserFavorites: (userId) => api.get(`/favoriterequests/user/${userId}`),
  checkFavorite: (userId, requestId) => api.get(`/favoriterequests/check?userId=${userId}&requestId=${requestId}`)
};

// Yapılacaklar API
export const todoAPI = {
  create: (data, userId) => api.post(`/todo?userId=${userId}`, data),
  getUserTodos: (userId) => api.get(`/todo/user/${userId}`),
  toggleComplete: (id, userId) => api.put(`/todo/${id}/complete?userId=${userId}`),
  delete: (id, userId) => api.delete(`/todo/${id}?userId=${userId}`),
  getAllForAdmin: () => api.get('/todo/admin/all')
};

// Anketler API
export const surveysAPI = {
  create: (data, adminId) => api.post(`/surveys?adminId=${adminId}`, data),
  getActive: () => api.get('/surveys/active'),
  respond: (data, userId) => api.post(`/surveys/respond?userId=${userId}`, data),
  getResponses: (surveyId, adminId) => api.get(`/surveys/${surveyId}/responses?adminId=${adminId}`),
  getUserAnswered: (userId) => api.get(`/surveys/user/${userId}/answered`),
  checkAnswered: (userId, surveyId) => api.get(`/surveys/check-answered?userId=${userId}&surveyId=${surveyId}`)
};

// Duyurular API
export const announcementsAPI = {
  create: (data, adminId) => api.post(`/announcements?adminId=${adminId}`, data),
  getAll: (type = null) => api.get(`/announcements${type ? `?type=${type}` : ''}`),
  update: (id, data, adminId) => api.put(`/announcements/${id}?adminId=${adminId}`, data),
  delete: (id, adminId) => api.delete(`/announcements/${id}?adminId=${adminId}`)
};

// Etkinlikler API
export const eventsAPI = {
  create: (data, adminId) => api.post(`/events?adminId=${adminId}`, data),
  getAll: (startDate = null, endDate = null) => {
    let url = '/events';
    const params = [];
    if (startDate) params.push(`startDate=${startDate}`);
    if (endDate) params.push(`endDate=${endDate}`);
    if (params.length > 0) url += `?${params.join('&')}`;
    return api.get(url);
  },
  getCalendar: (year, month) => api.get(`/events/calendar/${year}/${month}`),
  update: (id, data, adminId) => api.put(`/events/${id}?adminId=${adminId}`, data),
  delete: (id, adminId) => api.delete(`/events/${id}?adminId=${adminId}`)
};

// Profil API
export const profileAPI = {
  get: (userId) => api.get(`/profile/${userId}`),
  update: (userId, data) => api.put(`/profile/${userId}`, data),
  getSettings: (userId) => api.get(`/profile/${userId}/settings`),
  updateSettings: (userId, data) => api.put(`/profile/${userId}/settings`, data),
  getStats: (userId) => api.get(`/profile/${userId}/stats`)
};

export default api;
