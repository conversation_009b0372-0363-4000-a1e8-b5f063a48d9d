{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\metin_kayit\\\\talep-yonetim-frontend\\\\src\\\\components\\\\CreateRequest.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { requestsAPI } from '../services/api';\nimport { useAuth } from '../context/AuthContext';\nimport './CreateRequest.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CreateRequest = ({\n  onClose,\n  onRequestCreated\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    title: '',\n    content: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const {\n    user\n  } = useAuth();\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    try {\n      await requestsAPI.create(formData, user.id);\n      onRequestCreated();\n    } catch (error) {\n      var _error$response, _error$response$data;\n      setError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Talep oluşturulurken bir hata oluştu.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"create-request-overlay\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"create-request-modal\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Yeni Talep Olu\\u015Ftur\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onClose,\n          className: \"close-btn\",\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-body\",\n        children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"title\",\n              children: \"Talep Ba\\u015Fl\\u0131\\u011F\\u0131:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"title\",\n              name: \"title\",\n              value: formData.title,\n              onChange: handleChange,\n              required: true,\n              maxLength: \"200\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"content\",\n              children: \"Talep \\u0130\\xE7eri\\u011Fi:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              id: \"content\",\n              name: \"content\",\n              value: formData.content,\n              onChange: handleChange,\n              required: true,\n              rows: \"6\",\n              placeholder: \"Talebinizi detayl\\u0131 bir \\u015Fekilde a\\xE7\\u0131klay\\u0131n...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: onClose,\n              className: \"cancel-btn\",\n              children: \"\\u0130ptal\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              disabled: loading,\n              className: \"submit-btn\",\n              children: loading ? 'Oluşturuluyor...' : 'Talep Oluştur'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 38,\n    columnNumber: 5\n  }, this);\n};\n_s(CreateRequest, \"YFja4Ph62jq9FTag+Gpv+GnHUqI=\", false, function () {\n  return [useAuth];\n});\n_c = CreateRequest;\nexport default CreateRequest;\nvar _c;\n$RefreshReg$(_c, \"CreateRequest\");", "map": {"version": 3, "names": ["React", "useState", "requestsAPI", "useAuth", "jsxDEV", "_jsxDEV", "CreateRequest", "onClose", "onRequestCreated", "_s", "formData", "setFormData", "title", "content", "loading", "setLoading", "error", "setError", "user", "handleChange", "e", "target", "name", "value", "handleSubmit", "preventDefault", "create", "id", "_error$response", "_error$response$data", "response", "data", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onSubmit", "htmlFor", "type", "onChange", "required", "max<PERSON><PERSON><PERSON>", "rows", "placeholder", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/metin_kayit/talep-yonetim-frontend/src/components/CreateRequest.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { requestsAPI } from '../services/api';\nimport { useAuth } from '../context/AuthContext';\nimport './CreateRequest.css';\n\nconst CreateRequest = ({ onClose, onRequestCreated }) => {\n  const [formData, setFormData] = useState({\n    title: '',\n    content: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const { user } = useAuth();\n\n  const handleChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    try {\n      await requestsAPI.create(formData, user.id);\n      onRequestCreated();\n    } catch (error) {\n      setError(error.response?.data?.message || 'Talep oluşturulurken bir hata <PERSON>.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"create-request-overlay\">\n      <div className=\"create-request-modal\">\n        <div className=\"modal-header\">\n          <h3>Yeni Talep Oluştur</h3>\n          <button onClick={onClose} className=\"close-btn\">&times;</button>\n        </div>\n\n        <div className=\"modal-body\">\n          {error && <div className=\"error-message\">{error}</div>}\n          \n          <form onSubmit={handleSubmit}>\n            <div className=\"form-group\">\n              <label htmlFor=\"title\">Talep Başlığı:</label>\n              <input\n                type=\"text\"\n                id=\"title\"\n                name=\"title\"\n                value={formData.title}\n                onChange={handleChange}\n                required\n                maxLength=\"200\"\n              />\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"content\">Talep İçeriği:</label>\n              <textarea\n                id=\"content\"\n                name=\"content\"\n                value={formData.content}\n                onChange={handleChange}\n                required\n                rows=\"6\"\n                placeholder=\"Talebinizi detaylı bir şekilde açıklayın...\"\n              />\n            </div>\n\n            <div className=\"modal-actions\">\n              <button type=\"button\" onClick={onClose} className=\"cancel-btn\">\n                İptal\n              </button>\n              <button type=\"submit\" disabled={loading} className=\"submit-btn\">\n                {loading ? 'Oluşturuluyor...' : 'Talep Oluştur'}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CreateRequest;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,aAAa,GAAGA,CAAC;EAAEC,OAAO;EAAEC;AAAiB,CAAC,KAAK;EAAAC,EAAA;EACvD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC;IACvCW,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM;IAAEiB;EAAK,CAAC,GAAGf,OAAO,CAAC,CAAC;EAE1B,MAAMgB,YAAY,GAAIC,CAAC,IAAK;IAC1BT,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACU,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClBV,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMf,WAAW,CAACwB,MAAM,CAAChB,QAAQ,EAAEQ,IAAI,CAACS,EAAE,CAAC;MAC3CnB,gBAAgB,CAAC,CAAC;IACpB,CAAC,CAAC,OAAOQ,KAAK,EAAE;MAAA,IAAAY,eAAA,EAAAC,oBAAA;MACdZ,QAAQ,CAAC,EAAAW,eAAA,GAAAZ,KAAK,CAACc,QAAQ,cAAAF,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBG,IAAI,cAAAF,oBAAA,uBAApBA,oBAAA,CAAsBG,OAAO,KAAI,uCAAuC,CAAC;IACpF,CAAC,SAAS;MACRjB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEV,OAAA;IAAK4B,SAAS,EAAC,wBAAwB;IAAAC,QAAA,eACrC7B,OAAA;MAAK4B,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACnC7B,OAAA;QAAK4B,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B7B,OAAA;UAAA6B,QAAA,EAAI;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3BjC,OAAA;UAAQkC,OAAO,EAAEhC,OAAQ;UAAC0B,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,eAENjC,OAAA;QAAK4B,SAAS,EAAC,YAAY;QAAAC,QAAA,GACxBlB,KAAK,iBAAIX,OAAA;UAAK4B,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAElB;QAAK;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAEtDjC,OAAA;UAAMmC,QAAQ,EAAEhB,YAAa;UAAAU,QAAA,gBAC3B7B,OAAA;YAAK4B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB7B,OAAA;cAAOoC,OAAO,EAAC,OAAO;cAAAP,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7CjC,OAAA;cACEqC,IAAI,EAAC,MAAM;cACXf,EAAE,EAAC,OAAO;cACVL,IAAI,EAAC,OAAO;cACZC,KAAK,EAAEb,QAAQ,CAACE,KAAM;cACtB+B,QAAQ,EAAExB,YAAa;cACvByB,QAAQ;cACRC,SAAS,EAAC;YAAK;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENjC,OAAA;YAAK4B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB7B,OAAA;cAAOoC,OAAO,EAAC,SAAS;cAAAP,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/CjC,OAAA;cACEsB,EAAE,EAAC,SAAS;cACZL,IAAI,EAAC,SAAS;cACdC,KAAK,EAAEb,QAAQ,CAACG,OAAQ;cACxB8B,QAAQ,EAAExB,YAAa;cACvByB,QAAQ;cACRE,IAAI,EAAC,GAAG;cACRC,WAAW,EAAC;YAA6C;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENjC,OAAA;YAAK4B,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B7B,OAAA;cAAQqC,IAAI,EAAC,QAAQ;cAACH,OAAO,EAAEhC,OAAQ;cAAC0B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAE/D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjC,OAAA;cAAQqC,IAAI,EAAC,QAAQ;cAACM,QAAQ,EAAElC,OAAQ;cAACmB,SAAS,EAAC,YAAY;cAAAC,QAAA,EAC5DpB,OAAO,GAAG,kBAAkB,GAAG;YAAe;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7B,EAAA,CAlFIH,aAAa;EAAA,QAOAH,OAAO;AAAA;AAAA8C,EAAA,GAPpB3C,aAAa;AAoFnB,eAAeA,aAAa;AAAC,IAAA2C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}