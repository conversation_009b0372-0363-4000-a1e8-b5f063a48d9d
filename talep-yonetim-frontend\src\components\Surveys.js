import React, { useState, useEffect } from 'react';
import { surveysAPI } from '../services/api';
import { useAuth } from '../context/AuthContext';
import './Surveys.css';

const Surveys = () => {
  const [surveys, setSurveys] = useState([]);
  const [answeredSurveys, setAnsweredSurveys] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedSurvey, setSelectedSurvey] = useState(null);
  const [answers, setAnswers] = useState({ answer1: '', answer2: '', answer3: '' });
  const [activeTab, setActiveTab] = useState('available');
  const { user } = useAuth();

  useEffect(() => {
    fetchSurveys();
    fetchAnsweredSurveys();
  }, []);

  const fetchSurveys = async () => {
    try {
      setLoading(true);
      const response = await surveysAPI.getActive();
      setSurveys(response.data);
    } catch (error) {
      setError('Anketler yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const fetchAnsweredSurveys = async () => {
    try {
      const response = await surveysAPI.getUserAnswered(user.id);
      setAnsweredSurveys(response.data);
    } catch (error) {
      console.error('Cevaplanan anketler yüklenirken hata:', error);
    }
  };

  const handleAnswerSurvey = async (e) => {
    e.preventDefault();
    if (!answers.answer1.trim() || !answers.answer2.trim() || !answers.answer3.trim()) {
      setError('Lütfen tüm soruları cevaplayın.');
      return;
    }

    try {
      await surveysAPI.respond({
        surveyId: selectedSurvey.id,
        ...answers
      }, user.id);
      
      setSelectedSurvey(null);
      setAnswers({ answer1: '', answer2: '', answer3: '' });
      setError('');
      fetchSurveys();
      fetchAnsweredSurveys();
      alert('Anket cevabınız başarıyla kaydedildi!');
    } catch (error) {
      setError('Anket cevaplanırken bir hata oluştu.');
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('tr-TR');
  };

  const isExpired = (expiryDate) => {
    if (!expiryDate) return false;
    return new Date(expiryDate) < new Date();
  };

  if (loading) {
    return (
      <div className="surveys-container">
        <div className="loading">Anketler yükleniyor...</div>
      </div>
    );
  }

  return (
    <div className="surveys-container">
      <div className="surveys-header">
        <h2>📝 Anketler</h2>
        <p>Görüşlerinizi paylaşın ve anketlere katılın.</p>
      </div>

      {error && <div className="error-message">{error}</div>}

      <div className="survey-tabs">
        <button 
          className={`tab-btn ${activeTab === 'available' ? 'active' : ''}`}
          onClick={() => setActiveTab('available')}
        >
          📋 Mevcut Anketler ({surveys.length})
        </button>
        <button 
          className={`tab-btn ${activeTab === 'answered' ? 'active' : ''}`}
          onClick={() => setActiveTab('answered')}
        >
          ✅ Cevapladıklarım ({answeredSurveys.length})
        </button>
      </div>

      {activeTab === 'available' && (
        <div className="available-surveys">
          {surveys.length === 0 ? (
            <div className="no-surveys">
              <div className="no-surveys-icon">📝</div>
              <h3>Şu anda aktif anket yok</h3>
              <p>Yeni anketler eklendiğinde burada görünecek.</p>
            </div>
          ) : (
            <div className="surveys-grid">
              {surveys.map(survey => (
                <div key={survey.id} className="survey-card">
                  <div className="survey-header">
                    <h3>{survey.title}</h3>
                    {survey.description && <p>{survey.description}</p>}
                    <div className="survey-meta">
                      <span className="admin-name">👤 {survey.adminName}</span>
                      <span className="created-date">📅 {formatDate(survey.createdAt)}</span>
                      {survey.expiryDate && (
                        <span className={`expiry-date ${isExpired(survey.expiryDate) ? 'expired' : ''}`}>
                          ⏰ Son: {formatDate(survey.expiryDate)}
                        </span>
                      )}
                    </div>
                  </div>
                  
                  <div className="survey-stats">
                    <span className="response-count">
                      👥 {survey.responseCount} kişi cevapladı
                    </span>
                  </div>

                  <div className="survey-actions">
                    {isExpired(survey.expiryDate) ? (
                      <button className="expired-btn" disabled>
                        ⏰ Süresi Dolmuş
                      </button>
                    ) : (
                      <button 
                        onClick={() => setSelectedSurvey(survey)}
                        className="answer-btn"
                      >
                        📝 Anketi Cevapla
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {activeTab === 'answered' && (
        <div className="answered-surveys">
          {answeredSurveys.length === 0 ? (
            <div className="no-surveys">
              <div className="no-surveys-icon">✅</div>
              <h3>Henüz anket cevaplamadınız</h3>
              <p>Cevapladığınız anketler burada görünecek.</p>
            </div>
          ) : (
            <div className="surveys-grid">
              {answeredSurveys.map(item => (
                <div key={item.responseId} className="survey-card answered">
                  <div className="survey-header">
                    <h3>{item.survey.title}</h3>
                    {item.survey.description && <p>{item.survey.description}</p>}
                    <div className="survey-meta">
                      <span className="admin-name">👤 {item.survey.adminName}</span>
                      <span className="answered-date">✅ Cevaplandı: {formatDate(item.answeredAt)}</span>
                    </div>
                  </div>
                  
                  <div className="answers-preview">
                    <div className="answer-item">
                      <strong>S1:</strong> {item.survey.question1}
                      <div className="answer">C: {item.answer1}</div>
                    </div>
                    <div className="answer-item">
                      <strong>S2:</strong> {item.survey.question2}
                      <div className="answer">C: {item.answer2}</div>
                    </div>
                    <div className="answer-item">
                      <strong>S3:</strong> {item.survey.question3}
                      <div className="answer">C: {item.answer3}</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {selectedSurvey && (
        <div className="survey-modal">
          <div className="modal-content">
            <div className="modal-header">
              <h3>📝 {selectedSurvey.title}</h3>
              <button 
                onClick={() => setSelectedSurvey(null)}
                className="close-btn"
              >
                ✕
              </button>
            </div>
            
            {selectedSurvey.description && (
              <div className="survey-description">
                <p>{selectedSurvey.description}</p>
              </div>
            )}

            <form onSubmit={handleAnswerSurvey} className="survey-form">
              <div className="question-group">
                <label>1. {selectedSurvey.question1}</label>
                <textarea
                  value={answers.answer1}
                  onChange={(e) => setAnswers({...answers, answer1: e.target.value})}
                  placeholder="Cevabınızı yazın..."
                  rows="3"
                  required
                />
              </div>

              <div className="question-group">
                <label>2. {selectedSurvey.question2}</label>
                <textarea
                  value={answers.answer2}
                  onChange={(e) => setAnswers({...answers, answer2: e.target.value})}
                  placeholder="Cevabınızı yazın..."
                  rows="3"
                  required
                />
              </div>

              <div className="question-group">
                <label>3. {selectedSurvey.question3}</label>
                <textarea
                  value={answers.answer3}
                  onChange={(e) => setAnswers({...answers, answer3: e.target.value})}
                  placeholder="Cevabınızı yazın..."
                  rows="3"
                  required
                />
              </div>

              <div className="form-actions">
                <button type="submit" className="submit-btn">
                  📤 Cevapları Gönder
                </button>
                <button 
                  type="button" 
                  onClick={() => setSelectedSurvey(null)}
                  className="cancel-btn"
                >
                  İptal
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default Surveys;
