using System.ComponentModel.DataAnnotations;

namespace TalepYonetimAPI.Models
{
    public class SurveyResponse
    {
        public int Id { get; set; }
        
        public int SurveyId { get; set; }
        
        public int UserId { get; set; }
        
        [Required]
        public string Answer1 { get; set; } = string.Empty;
        
        [Required]
        public string Answer2 { get; set; } = string.Empty;
        
        [Required]
        public string Answer3 { get; set; } = string.Empty;
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        // Navigation properties
        public virtual Survey Survey { get; set; } = null!;
        public virtual User User { get; set; } = null!;
    }
}
