using System.ComponentModel.DataAnnotations;

namespace TalepYonetimAPI.Models
{
    public class Survey
    {
        public int Id { get; set; }
        
        public int AdminId { get; set; }
        
        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;
        
        public string? Description { get; set; }
        
        [Required]
        [StringLength(500)]
        public string Question1 { get; set; } = string.Empty;
        
        [Required]
        [StringLength(500)]
        public string Question2 { get; set; } = string.Empty;
        
        [Required]
        [StringLength(500)]
        public string Question3 { get; set; } = string.Empty;
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        public DateTime? ExpiryDate { get; set; }
        
        // Navigation properties
        public virtual User Admin { get; set; } = null!;
        public virtual ICollection<SurveyResponse> SurveyResponses { get; set; } = new List<SurveyResponse>();
    }
}
