{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\metin_kayit\\\\talep-yonetim-frontend\\\\src\\\\components\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { authAPI } from '../services/api';\nimport { useAuth } from '../context/AuthContext';\nimport './Login.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = ({\n  onSwitchToRegister\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    employeeNumber: '',\n    password: ''\n  });\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  const {\n    login\n  } = useAuth();\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    try {\n      const response = await authAPI.login(formData.employeeNumber, formData.password);\n      login(response.data.user);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      setError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Giriş yapılırken bir hata oluştu.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"login-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"login-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"login-header\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"company-logo\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"logo-placeholder\",\n            children: \"T\\xDCNA\\u015E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"company-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Talep Y\\xF6netim Sistemi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"T\\xFCrkiye N\\xFCkleer Enerji A.\\u015E.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 19\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"employeeNumber\",\n            children: \"Personel Numaras\\u0131:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            id: \"employeeNumber\",\n            name: \"employeeNumber\",\n            value: formData.employeeNumber,\n            onChange: handleChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"password\",\n            children: \"\\u015Eifre:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            id: \"password\",\n            name: \"password\",\n            value: formData.password,\n            onChange: handleChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          disabled: loading,\n          className: \"submit-btn\",\n          children: loading ? 'Giriş yapılıyor...' : 'Giriş Yap'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"switch-form\",\n        children: [\"Hesab\\u0131n\\u0131z yok mu?\", /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: onSwitchToRegister,\n          className: \"link-btn\",\n          children: \"Kay\\u0131t Ol\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 38,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"+/UnNfz+q4DiB8jw3i0vF0PIPsM=\", false, function () {\n  return [useAuth];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "authAPI", "useAuth", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "onSwitchToRegister", "_s", "formData", "setFormData", "employeeNumber", "password", "error", "setError", "loading", "setLoading", "login", "handleChange", "e", "target", "name", "value", "handleSubmit", "preventDefault", "response", "data", "user", "_error$response", "_error$response$data", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "type", "id", "onChange", "required", "disabled", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/metin_kayit/talep-yonetim-frontend/src/components/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { authAPI } from '../services/api';\nimport { useAuth } from '../context/AuthContext';\nimport './Login.css';\n\nconst Login = ({ onSwitchToRegister }) => {\n  const [formData, setFormData] = useState({\n    employeeNumber: '',\n    password: ''\n  });\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  const { login } = useAuth();\n\n  const handleChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    try {\n      const response = await authAPI.login(formData.employeeNumber, formData.password);\n      login(response.data.user);\n    } catch (error) {\n      setError(error.response?.data?.message || '<PERSON><PERSON><PERSON> ya<PERSON>ılırken bir hata oluştu.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"login-container\">\n      <div className=\"login-form\">\n        <div className=\"login-header\">\n          <div className=\"company-logo\">\n            <div className=\"logo-placeholder\">TÜNAŞ</div>\n            <div className=\"company-info\">\n              <h2>Talep Yönetim Sistemi</h2>\n              <p>Türkiye Nükleer Enerji A.Ş.</p>\n            </div>\n          </div>\n        </div>\n        {error && <div className=\"error-message\">{error}</div>}\n        \n        <form onSubmit={handleSubmit}>\n          <div className=\"form-group\">\n            <label htmlFor=\"employeeNumber\">Personel Numarası:</label>\n            <input\n              type=\"text\"\n              id=\"employeeNumber\"\n              name=\"employeeNumber\"\n              value={formData.employeeNumber}\n              onChange={handleChange}\n              required\n            />\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"password\">Şifre:</label>\n            <input\n              type=\"password\"\n              id=\"password\"\n              name=\"password\"\n              value={formData.password}\n              onChange={handleChange}\n              required\n            />\n          </div>\n\n          <button type=\"submit\" disabled={loading} className=\"submit-btn\">\n            {loading ? 'Giriş yapılıyor...' : 'Giriş Yap'}\n          </button>\n        </form>\n\n        <p className=\"switch-form\">\n          Hesabınız yok mu? \n          <button type=\"button\" onClick={onSwitchToRegister} className=\"link-btn\">\n            Kayıt Ol\n          </button>\n        </p>\n      </div>\n    </div>\n  );\n};\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,MAAMC,KAAK,GAAGA,CAAC;EAAEC;AAAmB,CAAC,KAAK;EAAAC,EAAA;EACxC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC;IACvCU,cAAc,EAAE,EAAE;IAClBC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM;IAAEgB;EAAM,CAAC,GAAGd,OAAO,CAAC,CAAC;EAE3B,MAAMe,YAAY,GAAIC,CAAC,IAAK;IAC1BT,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACU,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClBR,UAAU,CAAC,IAAI,CAAC;IAChBF,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMW,QAAQ,GAAG,MAAMvB,OAAO,CAACe,KAAK,CAACR,QAAQ,CAACE,cAAc,EAAEF,QAAQ,CAACG,QAAQ,CAAC;MAChFK,KAAK,CAACQ,QAAQ,CAACC,IAAI,CAACC,IAAI,CAAC;IAC3B,CAAC,CAAC,OAAOd,KAAK,EAAE;MAAA,IAAAe,eAAA,EAAAC,oBAAA;MACdf,QAAQ,CAAC,EAAAc,eAAA,GAAAf,KAAK,CAACY,QAAQ,cAAAG,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBF,IAAI,cAAAG,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI,mCAAmC,CAAC;IAChF,CAAC,SAAS;MACRd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEX,OAAA;IAAK0B,SAAS,EAAC,iBAAiB;IAAAC,QAAA,eAC9B3B,OAAA;MAAK0B,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzB3B,OAAA;QAAK0B,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3B3B,OAAA;UAAK0B,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B3B,OAAA;YAAK0B,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC7C/B,OAAA;YAAK0B,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B3B,OAAA;cAAA2B,QAAA,EAAI;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9B/B,OAAA;cAAA2B,QAAA,EAAG;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACLvB,KAAK,iBAAIR,OAAA;QAAK0B,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAEnB;MAAK;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEtD/B,OAAA;QAAMgC,QAAQ,EAAEd,YAAa;QAAAS,QAAA,gBAC3B3B,OAAA;UAAK0B,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB3B,OAAA;YAAOiC,OAAO,EAAC,gBAAgB;YAAAN,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC1D/B,OAAA;YACEkC,IAAI,EAAC,MAAM;YACXC,EAAE,EAAC,gBAAgB;YACnBnB,IAAI,EAAC,gBAAgB;YACrBC,KAAK,EAAEb,QAAQ,CAACE,cAAe;YAC/B8B,QAAQ,EAAEvB,YAAa;YACvBwB,QAAQ;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN/B,OAAA;UAAK0B,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB3B,OAAA;YAAOiC,OAAO,EAAC,UAAU;YAAAN,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACxC/B,OAAA;YACEkC,IAAI,EAAC,UAAU;YACfC,EAAE,EAAC,UAAU;YACbnB,IAAI,EAAC,UAAU;YACfC,KAAK,EAAEb,QAAQ,CAACG,QAAS;YACzB6B,QAAQ,EAAEvB,YAAa;YACvBwB,QAAQ;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN/B,OAAA;UAAQkC,IAAI,EAAC,QAAQ;UAACI,QAAQ,EAAE5B,OAAQ;UAACgB,SAAS,EAAC,YAAY;UAAAC,QAAA,EAC5DjB,OAAO,GAAG,oBAAoB,GAAG;QAAW;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEP/B,OAAA;QAAG0B,SAAS,EAAC,aAAa;QAAAC,QAAA,GAAC,6BAEzB,eAAA3B,OAAA;UAAQkC,IAAI,EAAC,QAAQ;UAACK,OAAO,EAAErC,kBAAmB;UAACwB,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAExE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5B,EAAA,CApFIF,KAAK;EAAA,QAOSH,OAAO;AAAA;AAAA0C,EAAA,GAPrBvC,KAAK;AAsFX,eAAeA,KAAK;AAAC,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}