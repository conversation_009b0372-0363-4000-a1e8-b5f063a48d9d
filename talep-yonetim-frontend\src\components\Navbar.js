import React, { useState } from 'react';
import { useAuth } from '../context/AuthContext';
import './Navbar.css';
// Logo import - logo dosyasını src/assets/images/logos/ klasörüne koyun
import tunasLogo from '../assets/images/logos/tunas-logo-white.png';

const Navbar = ({ onShowAbout, onNavigate }) => {
  const [darkMode, setDarkMode] = useState(false);
  const [showProfile, setShowProfile] = useState(false);
  const { user, logout, isAdmin } = useAuth();

  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
    // Dark mode functionality can be implemented later
    console.log('Dark mode:', !darkMode);
  };

  return (
    <nav className="navbar">
      <div className="navbar-container">
        <div className="navbar-brand">
          <div className="logo-container">
            <img src={tunasLogo} alt="TÜNAŞ Logo" className="navbar-logo" />
          </div>
        </div>

        <div className="navbar-menu">
          <div className="navbar-nav">
            <button
              onClick={onShowAbout}
              className="nav-link nav-button"
            >
              <i className="icon-info"></i>
              TÜNAŞ Hakkında
            </button>
          </div>

          <div className="navbar-actions">
            <button 
              className="action-btn"
              onClick={toggleDarkMode}
              title="Gece Modu"
            >
              <i className={`icon-${darkMode ? 'sun' : 'moon'}`}></i>
            </button>

            <button 
              className="action-btn"
              title="Ayarlar"
            >
              <i className="icon-settings"></i>
            </button>

            <div className="profile-dropdown">
              <button 
                className="profile-btn"
                onClick={() => setShowProfile(!showProfile)}
              >
                <div className="profile-avatar">
                  {user.firstName.charAt(0)}{user.lastName.charAt(0)}
                </div>
                <span className="profile-name">
                  {user.firstName} {user.lastName}
                </span>
                <i className="icon-chevron-down"></i>
              </button>

              {showProfile && (
                <div className="profile-menu">
                  <div className="profile-header">
                    <div className="profile-avatar large">
                      {user.firstName.charAt(0)}{user.lastName.charAt(0)}
                    </div>
                    <div className="profile-info">
                      <h4>{user.firstName} {user.lastName}</h4>
                      <p>{user.employeeNumber}</p>
                      <p>{user.department}</p>
                      <span className={`role-badge ${user.userType.toLowerCase()}`}>
                        {user.userType === 'Admin' ? 'Yönetici' : 'Kullanıcı'}
                      </span>
                    </div>
                  </div>
                  
                  <div className="profile-actions">
                    <button className="profile-action">
                      <i className="icon-user"></i>
                      Profil Bilgilerim
                    </button>
                    <button className="profile-action">
                      <i className="icon-edit"></i>
                      Profil Düzenle
                    </button>
                    <button className="profile-action">
                      <i className="icon-settings"></i>
                      Ayarlar
                    </button>
                    <button className="profile-action">
                      <i className="icon-help"></i>
                      Yardım
                    </button>
                    <hr />
                    <button className="profile-action logout" onClick={logout}>
                      <i className="icon-logout"></i>
                      Çıkış Yap
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
