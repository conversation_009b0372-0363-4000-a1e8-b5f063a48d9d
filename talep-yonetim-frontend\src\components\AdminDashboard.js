import React, { useState, useEffect } from 'react';
import { requestsAPI, responsesAPI } from '../services/api';
import { useAuth } from '../context/AuthContext';
import Navbar from './Navbar';
import Footer from './Footer';
import AboutTunas from './AboutTunas';
import TodoManagement from './TodoManagement';
import SurveyCreate from './SurveyCreate';
import AnnouncementManagement from './AnnouncementManagement';
import EventManagement from './EventManagement';
import './AdminDashboard.css';

const AdminDashboard = () => {
  const [requests, setRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [responseText, setResponseText] = useState('');
  const [submittingResponse, setSubmittingResponse] = useState(false);
  const [showAbout, setShowAbout] = useState(false);
  const [activeView, setActiveView] = useState('requests'); // Başlangıç: Talepler
  const { user } = useAuth();

  useEffect(() => {
    fetchAllRequests();
  }, []);

  const fetchAllRequests = async () => {
    try {
      setLoading(true);
      const response = await requestsAPI.getAllForAdmin();
      setRequests(response.data);
    } catch (error) {
      setError('Talepler yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const handleResponseSubmit = async (e) => {
    e.preventDefault();
    if (!responseText.trim()) return;

    setSubmittingResponse(true);
    try {
      await responsesAPI.create({
        requestId: selectedRequest.id,
        content: responseText
      }, user.id);
      
      setResponseText('');
      setSelectedRequest(null);
      fetchAllRequests(); // Refresh the list
    } catch (error) {
      setError('Cevap gönderilirken bir hata oluştu.');
    } finally {
      setSubmittingResponse(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('tr-TR');
  };

  const pendingRequests = requests.filter(req => req.status === 'Pending');
  const answeredRequests = requests.filter(req => req.status === 'Answered');

  const renderNavigation = () => {
    const navItems = [
      { key: 'requests', icon: '📋', label: 'Talepler' },
      { key: 'todo-management', icon: '✅', label: 'Yapılacaklar Yönetimi' },
      { key: 'survey-create', icon: '📝', label: 'Anket Oluştur' },
      { key: 'announcements-create', icon: '📰', label: 'Duyuru/Haber Yönetimi' },
      { key: 'events-create', icon: '📅', label: 'Etkinlik Yönetimi' }
    ];

    return (
      <div className="dashboard-navigation">
        <div className="nav-tabs">
          {navItems.map(item => (
            <button
              key={item.key}
              onClick={() => setActiveView(item.key)}
              className={`nav-tab ${activeView === item.key ? 'active' : ''}`}
            >
              <span className="nav-icon">{item.icon}</span>
              <span className="nav-label">{item.label}</span>
            </button>
          ))}
        </div>
      </div>
    );
  };

  const renderContent = () => {
    switch (activeView) {
      case 'todo-management':
        return <TodoManagement />;
      case 'survey-create':
        return <SurveyCreate />;
      case 'announcements-create':
        return <AnnouncementManagement />;
      case 'events-create':
        return <EventManagement />;
      case 'requests':
      default:
        return (
          <div className="dashboard-content">
            <div className="stats-section">
              <div className="stat-card">
                <h3>Bekleyen Talepler</h3>
                <div className="stat-number">{pendingRequests.length}</div>
              </div>
              <div className="stat-card">
                <h3>Cevaplanmış Talepler</h3>
                <div className="stat-number">{answeredRequests.length}</div>
              </div>
              <div className="stat-card">
                <h3>Toplam Talepler</h3>
                <div className="stat-number">{requests.length}</div>
              </div>
            </div>

            {error && <div className="error-message">{error}</div>}

            {loading ? (
              <div className="loading">Talepler yükleniyor...</div>
            ) : (
              <div className="requests-section">
                <h2>Tüm Talepler</h2>

                <div className="requests-tabs">
                  <div className="tab pending">
                    <h3>Bekleyen Talepler ({pendingRequests.length})</h3>
                    {pendingRequests.length === 0 ? (
                      <div className="no-requests">Bekleyen talep bulunmuyor.</div>
                    ) : (
                      <div className="requests-list">
                        {pendingRequests.map(request => (
                          <div key={request.id} className="request-card">
                            <div className="request-header">
                              <h4>{request.title}</h4>
                              <span className="status pending">Beklemede</span>
                            </div>

                            <div className="request-content">
                              <p><strong>Kullanıcı:</strong> {request.user?.name || request.userName} ({request.user?.employeeNumber || 'N/A'})</p>
                              <p><strong>Departman:</strong> {request.user?.department || 'N/A'}</p>
                              <p><strong>İçerik:</strong> {request.content}</p>
                              {request.summary && (
                                <p><strong>AI Özeti:</strong> {request.summary}</p>
                              )}
                              <p><strong>Oluşturulma Tarihi:</strong> {formatDate(request.createdAt)}</p>
                            </div>

                            <div className="request-actions">
                              <button
                                onClick={() => setSelectedRequest(request)}
                                className="respond-btn"
                              >
                                Cevapla
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  <div className="tab answered">
                    <h3>Cevaplanmış Talepler ({answeredRequests.length})</h3>
                    {answeredRequests.length === 0 ? (
                      <div className="no-requests">Cevaplanmış talep bulunmuyor.</div>
                    ) : (
                      <div className="requests-list">
                        {answeredRequests.map(request => (
                          <div key={request.id} className="request-card">
                            <div className="request-header">
                              <h4>{request.title}</h4>
                              <span className="status answered">Cevaplanmış</span>
                            </div>

                            <div className="request-content">
                              <p><strong>Kullanıcı:</strong> {request.user?.name || request.userName} ({request.user?.employeeNumber || 'N/A'})</p>
                              <p><strong>Departman:</strong> {request.user?.department || 'N/A'}</p>
                              <p><strong>İçerik:</strong> {request.content}</p>
                              {request.summary && (
                                <p><strong>AI Özeti:</strong> {request.summary}</p>
                              )}
                              <p><strong>Oluşturulma Tarihi:</strong> {formatDate(request.createdAt)}</p>
                            </div>

                            {request.responses && request.responses.length > 0 && (
                              <div className="responses-section">
                                <h5>Verilen Cevaplar:</h5>
                                {request.responses.map(response => (
                                  <div key={response.id} className="response-item">
                                    <p>{response.content}</p>
                                    <small>
                                      {response.adminName} - {formatDate(response.createdAt)}
                                    </small>
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        );
    }
  };

  return (
    <div className="admin-dashboard">
      <Navbar onShowAbout={() => setShowAbout(true)} />

      {renderNavigation()}

      <main className="dashboard-main">
        {renderContent()}
      </main>

      <Footer />

      {showAbout && (
        <AboutTunas onClose={() => setShowAbout(false)} />
      )}

      {selectedRequest && (
        <div className="response-modal-overlay">
          <div className="response-modal">
            <div className="modal-header">
              <h3>Talebe Cevap Ver</h3>
              <button
                onClick={() => setSelectedRequest(null)}
                className="close-btn"
              >
                ✕
              </button>
            </div>

            <div className="request-details">
              <h4>{selectedRequest.title}</h4>
              <p><strong>Kullanıcı:</strong> {selectedRequest.userName}</p>
              <p><strong>İçerik:</strong> {selectedRequest.content}</p>
              {selectedRequest.summary && (
                <p><strong>AI Özeti:</strong> {selectedRequest.summary}</p>
              )}
            </div>

            <form onSubmit={handleResponseSubmit} className="response-form">
              <div className="form-group">
                <label htmlFor="response">Cevabınız:</label>
                <textarea
                  id="response"
                  value={responseText}
                  onChange={(e) => setResponseText(e.target.value)}
                  placeholder="Talebe cevabınızı yazın..."
                  rows="5"
                  required
                />
              </div>

              <div className="form-actions">
                <button
                  type="button"
                  onClick={() => setSelectedRequest(null)}
                  className="cancel-btn"
                >
                  İptal
                </button>
                <button
                  type="submit"
                  disabled={submittingResponse}
                  className="submit-btn"
                >
                  {submittingResponse ? 'Gönderiliyor...' : 'Cevap Gönder'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminDashboard;
