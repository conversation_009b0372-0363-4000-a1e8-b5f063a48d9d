import React, { useState, useEffect } from 'react';
import { favoritesAPI } from '../services/api';
import { useAuth } from '../context/AuthContext';
import './FavoriteRequests.css';

const FavoriteRequests = () => {
  const [favorites, setFavorites] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const { user } = useAuth();

  useEffect(() => {
    fetchFavorites();
  }, []);

  const fetchFavorites = async () => {
    try {
      setLoading(true);
      const response = await favoritesAPI.getUserFavorites(user.id);
      setFavorites(response.data);
    } catch (error) {
      setError('Favori talepler yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const removeFromFavorites = async (requestId) => {
    try {
      await favoritesAPI.remove(user.id, requestId);
      setFavorites(favorites.filter(fav => fav.request.id !== requestId));
    } catch (error) {
      setError('Favorilerden kaldırılırken bir hata oluştu.');
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('tr-TR');
  };

  if (loading) {
    return (
      <div className="favorites-container">
        <div className="loading">Favori talepler yükleniyor...</div>
      </div>
    );
  }

  return (
    <div className="favorites-container">
      <div className="favorites-header">
        <h2>⭐ Favori Taleplerim</h2>
        <p>Favorilere eklediğiniz talepler burada görünür.</p>
      </div>

      {error && <div className="error-message">{error}</div>}

      {favorites.length === 0 ? (
        <div className="no-favorites">
          <div className="no-favorites-icon">⭐</div>
          <h3>Henüz favori talebiniz yok</h3>
          <p>Beğendiğiniz talepleri favorilere ekleyerek buradan kolayca erişebilirsiniz.</p>
        </div>
      ) : (
        <div className="favorites-list">
          {favorites.map(favorite => (
            <div key={favorite.favoriteId} className="favorite-card">
              <div className="favorite-header">
                <div className="favorite-info">
                  <h3>{favorite.request.title}</h3>
                  <div className="favorite-meta">
                    <span className="added-date">
                      📅 Favorilere eklendi: {formatDate(favorite.addedAt)}
                    </span>
                    <span className={`status ${favorite.request.status.toLowerCase()}`}>
                      {favorite.request.status === 'Pending' ? 'Beklemede' : 'Cevaplanmış'}
                    </span>
                  </div>
                </div>
                <button 
                  onClick={() => removeFromFavorites(favorite.request.id)}
                  className="remove-favorite-btn"
                  title="Favorilerden kaldır"
                >
                  ❌
                </button>
              </div>

              <div className="request-content">
                <div className="content-section">
                  <strong>İçerik:</strong>
                  <p>{favorite.request.content}</p>
                </div>

                {favorite.request.summary && (
                  <div className="content-section">
                    <strong>AI Özeti:</strong>
                    <p className="ai-summary">{favorite.request.summary}</p>
                  </div>
                )}

                <div className="content-section">
                  <strong>Oluşturulma Tarihi:</strong>
                  <span>{formatDate(favorite.request.createdAt)}</span>
                </div>
              </div>

              {favorite.request.responses && favorite.request.responses.length > 0 && (
                <div className="responses-section">
                  <h4>📝 Admin Cevapları:</h4>
                  {favorite.request.responses.map(response => (
                    <div key={response.id} className="response-item">
                      <div className="response-content">
                        <p>{response.content}</p>
                      </div>
                      <div className="response-meta">
                        <span className="admin-name">👤 {response.adminName}</span>
                        <span className="response-date">📅 {formatDate(response.createdAt)}</span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default FavoriteRequests;
