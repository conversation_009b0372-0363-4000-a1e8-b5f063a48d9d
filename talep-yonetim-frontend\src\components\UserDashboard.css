.user-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  display: flex;
  flex-direction: column;
}

/* Dashboard Navigation */
.dashboard-navigation {
  background: white;
  border-bottom: 2px solid #e5e7eb;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-tabs {
  display: flex;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.nav-tabs::-webkit-scrollbar {
  display: none;
}

.nav-tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  color: var(--tunas-gray);
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
  white-space: nowrap;
  min-width: fit-content;
}

.nav-tab:hover {
  color: var(--tunas-primary);
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
}

.nav-tab.active {
  color: var(--tunas-accent);
  border-bottom-color: var(--tunas-accent);
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
}

.nav-icon {
  font-size: 1.2rem;
}

.nav-label {
  font-weight: 500;
}

.dashboard-main {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem 1rem;
  flex: 1;
}

.dashboard-actions {
  margin-bottom: 2rem;
}

.create-request-btn {
  background: linear-gradient(135deg, var(--tunas-accent) 0%, #059669 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.create-request-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.create-request-btn::before {
  content: '➕';
  font-size: 1.2rem;
}

.requests-section h2 {
  color: #333;
  margin-bottom: 1rem;
}

.loading, .no-requests {
  text-align: center;
  padding: 2rem;
  color: #666;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 0.75rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  border: 1px solid #f5c6cb;
}

.requests-list {
  display: grid;
  gap: 1rem;
}

.request-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border-left: 4px solid #007bff;
}

.request-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.request-header h3 {
  margin: 0;
  color: #333;
  flex: 1;
}

.status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
}

.status.pending {
  background-color: #fff3cd;
  color: #856404;
}

.status.answered {
  background-color: #d4edda;
  color: #155724;
}

.request-content p {
  margin: 0.5rem 0;
  color: #555;
}

.request-content strong {
  color: #333;
}

.responses-section {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
}

.responses-section h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.response-item {
  background-color: #f8f9fa;
  padding: 0.75rem;
  border-radius: 4px;
  margin-bottom: 0.5rem;
}

.response-item p {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.response-item small {
  color: #666;
  font-style: italic;
}

/* Navigation Responsive */
@media (max-width: 768px) {
  .nav-tab {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }

  .nav-label {
    display: none;
  }

  .nav-icon {
    font-size: 1.5rem;
  }
}
