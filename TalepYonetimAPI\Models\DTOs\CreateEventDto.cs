using System.ComponentModel.DataAnnotations;

namespace TalepYonetimAPI.Models.DTOs
{
    public class CreateEventDto
    {
        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;
        
        public string? Description { get; set; }
        
        [Required]
        public DateTime StartDate { get; set; }
        
        [Required]
        public DateTime EndDate { get; set; }
        
        [StringLength(200)]
        public string? Location { get; set; }
        
        [StringLength(50)]
        public string? EventType { get; set; }
        
        public bool IsAllDay { get; set; } = false;
        
        [StringLength(7)]
        public string Color { get; set; } = "#007bff";
    }
}
