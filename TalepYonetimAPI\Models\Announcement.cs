using System.ComponentModel.DataAnnotations;

namespace TalepYonetimAPI.Models
{
    public class Announcement
    {
        public int Id { get; set; }
        
        public int AdminId { get; set; }
        
        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;
        
        [Required]
        public string Content { get; set; } = string.Empty;
        
        [Required]
        [StringLength(20)]
        public string Type { get; set; } = string.Empty; // 'Announcement' veya 'News'
        
        public bool IsImportant { get; set; } = false;
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
        
        // Navigation properties
        public virtual User Admin { get; set; } = null!;
    }
}
