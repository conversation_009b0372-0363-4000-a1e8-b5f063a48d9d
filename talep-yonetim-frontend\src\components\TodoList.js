import React, { useState, useEffect } from 'react';
import { todoAPI } from '../services/api';
import { useAuth } from '../context/AuthContext';
import './TodoList.css';

const TodoList = () => {
  const [todos, setTodos] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showAddForm, setShowAddForm] = useState(false);
  const [newTodo, setNewTodo] = useState({
    title: '',
    description: '',
    priority: 'Normal',
    dueDate: ''
  });
  const { user } = useAuth();

  useEffect(() => {
    fetchTodos();
  }, []);

  const fetchTodos = async () => {
    try {
      setLoading(true);
      const response = await todoAPI.getUserTodos(user.id);
      setTodos(response.data);
    } catch (error) {
      setError('Yapılacaklar yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const handleAddTodo = async (e) => {
    e.preventDefault();
    if (!newTodo.title.trim()) return;

    try {
      await todoAPI.create(newTodo, user.id);
      setNewTodo({ title: '', description: '', priority: 'Normal', dueDate: '' });
      setShowAddForm(false);
      fetchTodos();
    } catch (error) {
      setError('Yapılacak eklenirken bir hata oluştu.');
    }
  };

  const toggleComplete = async (id) => {
    try {
      await todoAPI.toggleComplete(id, user.id);
      fetchTodos();
    } catch (error) {
      setError('Durum güncellenirken bir hata oluştu.');
    }
  };

  const deleteTodo = async (id) => {
    if (!window.confirm('Bu yapılacağı silmek istediğinizden emin misiniz?')) return;
    
    try {
      await todoAPI.delete(id, user.id);
      fetchTodos();
    } catch (error) {
      setError('Yapılacak silinirken bir hata oluştu.');
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('tr-TR');
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'Urgent': return '#dc2626';
      case 'High': return '#ea580c';
      case 'Normal': return '#2563eb';
      case 'Low': return '#16a34a';
      default: return '#2563eb';
    }
  };

  const getPriorityText = (priority) => {
    switch (priority) {
      case 'Urgent': return 'Acil';
      case 'High': return 'Yüksek';
      case 'Normal': return 'Normal';
      case 'Low': return 'Düşük';
      default: return 'Normal';
    }
  };

  if (loading) {
    return (
      <div className="todo-container">
        <div className="loading">Yapılacaklar yükleniyor...</div>
      </div>
    );
  }

  const completedTodos = todos.filter(todo => todo.isCompleted);
  const pendingTodos = todos.filter(todo => !todo.isCompleted);

  return (
    <div className="todo-container">
      <div className="todo-header">
        <h2>✅ Yapılacaklar Listem</h2>
        <p>Görevlerinizi organize edin ve takip edin.</p>
        <button 
          onClick={() => setShowAddForm(!showAddForm)}
          className="add-todo-btn"
        >
          ➕ Yeni Yapılacak Ekle
        </button>
      </div>

      {error && <div className="error-message">{error}</div>}

      {showAddForm && (
        <div className="add-todo-form">
          <h3>Yeni Yapılacak Ekle</h3>
          <form onSubmit={handleAddTodo}>
            <div className="form-group">
              <label>Başlık *</label>
              <input
                type="text"
                value={newTodo.title}
                onChange={(e) => setNewTodo({...newTodo, title: e.target.value})}
                placeholder="Yapılacak başlığı..."
                required
              />
            </div>
            
            <div className="form-group">
              <label>Açıklama</label>
              <textarea
                value={newTodo.description}
                onChange={(e) => setNewTodo({...newTodo, description: e.target.value})}
                placeholder="Detaylı açıklama..."
                rows="3"
              />
            </div>
            
            <div className="form-row">
              <div className="form-group">
                <label>Öncelik</label>
                <select
                  value={newTodo.priority}
                  onChange={(e) => setNewTodo({...newTodo, priority: e.target.value})}
                >
                  <option value="Low">Düşük</option>
                  <option value="Normal">Normal</option>
                  <option value="High">Yüksek</option>
                  <option value="Urgent">Acil</option>
                </select>
              </div>
              
              <div className="form-group">
                <label>Bitiş Tarihi</label>
                <input
                  type="date"
                  value={newTodo.dueDate}
                  onChange={(e) => setNewTodo({...newTodo, dueDate: e.target.value})}
                />
              </div>
            </div>
            
            <div className="form-actions">
              <button type="submit" className="submit-btn">Ekle</button>
              <button 
                type="button" 
                onClick={() => setShowAddForm(false)}
                className="cancel-btn"
              >
                İptal
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="todo-stats">
        <div className="stat-item">
          <span className="stat-number">{pendingTodos.length}</span>
          <span className="stat-label">Bekleyen</span>
        </div>
        <div className="stat-item">
          <span className="stat-number">{completedTodos.length}</span>
          <span className="stat-label">Tamamlanan</span>
        </div>
        <div className="stat-item">
          <span className="stat-number">{todos.length}</span>
          <span className="stat-label">Toplam</span>
        </div>
      </div>

      {todos.length === 0 ? (
        <div className="no-todos">
          <div className="no-todos-icon">📝</div>
          <h3>Henüz yapılacağınız yok</h3>
          <p>İlk yapılacağınızı ekleyerek başlayın!</p>
        </div>
      ) : (
        <div className="todo-sections">
          {pendingTodos.length > 0 && (
            <div className="todo-section">
              <h3>🔄 Bekleyen Görevler ({pendingTodos.length})</h3>
              <div className="todo-list">
                {pendingTodos.map(todo => (
                  <div key={todo.id} className="todo-item">
                    <div className="todo-content">
                      <div className="todo-main">
                        <button 
                          onClick={() => toggleComplete(todo.id)}
                          className="complete-btn"
                        >
                          ⭕
                        </button>
                        <div className="todo-info">
                          <h4>{todo.title}</h4>
                          {todo.description && <p>{todo.description}</p>}
                          <div className="todo-meta">
                            <span 
                              className="priority"
                              style={{ backgroundColor: getPriorityColor(todo.priority) }}
                            >
                              {getPriorityText(todo.priority)}
                            </span>
                            {todo.dueDate && (
                              <span className="due-date">
                                📅 {formatDate(todo.dueDate)}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                      <button 
                        onClick={() => deleteTodo(todo.id)}
                        className="delete-btn"
                        title="Sil"
                      >
                        🗑️
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {completedTodos.length > 0 && (
            <div className="todo-section">
              <h3>✅ Tamamlanan Görevler ({completedTodos.length})</h3>
              <div className="todo-list">
                {completedTodos.map(todo => (
                  <div key={todo.id} className="todo-item completed">
                    <div className="todo-content">
                      <div className="todo-main">
                        <button 
                          onClick={() => toggleComplete(todo.id)}
                          className="complete-btn"
                        >
                          ✅
                        </button>
                        <div className="todo-info">
                          <h4>{todo.title}</h4>
                          {todo.description && <p>{todo.description}</p>}
                          <div className="todo-meta">
                            <span 
                              className="priority"
                              style={{ backgroundColor: getPriorityColor(todo.priority) }}
                            >
                              {getPriorityText(todo.priority)}
                            </span>
                            {todo.dueDate && (
                              <span className="due-date">
                                📅 {formatDate(todo.dueDate)}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                      <button 
                        onClick={() => deleteTodo(todo.id)}
                        className="delete-btn"
                        title="Sil"
                      >
                        🗑️
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default TodoList;
