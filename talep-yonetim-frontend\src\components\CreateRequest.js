import React, { useState } from 'react';
import { requestsAPI } from '../services/api';
import { useAuth } from '../context/AuthContext';
import './CreateRequest.css';

const CreateRequest = ({ onClose, onRequestCreated }) => {
  const [formData, setFormData] = useState({
    title: '',
    content: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const { user } = useAuth();

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      await requestsAPI.create(formData, user.id);
      onRequestCreated();
    } catch (error) {
      setError(error.response?.data?.message || 'Talep oluşturulurken bir hata <PERSON>.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="create-request-overlay">
      <div className="create-request-modal">
        <div className="modal-header">
          <h3>Yeni Talep Oluştur</h3>
          <button onClick={onClose} className="close-btn">&times;</button>
        </div>

        <div className="modal-body">
          {error && <div className="error-message">{error}</div>}
          
          <form onSubmit={handleSubmit}>
            <div className="form-group">
              <label htmlFor="title">Talep Başlığı:</label>
              <input
                type="text"
                id="title"
                name="title"
                value={formData.title}
                onChange={handleChange}
                required
                maxLength="200"
              />
            </div>

            <div className="form-group">
              <label htmlFor="content">Talep İçeriği:</label>
              <textarea
                id="content"
                name="content"
                value={formData.content}
                onChange={handleChange}
                required
                rows="6"
                placeholder="Talebinizi detaylı bir şekilde açıklayın..."
              />
            </div>

            <div className="modal-actions">
              <button type="button" onClick={onClose} className="cancel-btn">
                İptal
              </button>
              <button type="submit" disabled={loading} className="submit-btn">
                {loading ? 'Oluşturuluyor...' : 'Talep Oluştur'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default CreateRequest;
