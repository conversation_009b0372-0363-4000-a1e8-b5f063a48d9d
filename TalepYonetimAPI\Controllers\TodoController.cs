using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TalepYonetimAPI.Data;
using TalepYonetimAPI.Models;
using TalepYonetimAPI.Models.DTOs;

namespace TalepYonetimAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class TodoController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<TodoController> _logger;

        public TodoController(ApplicationDbContext context, ILogger<TodoController> logger)
        {
            _context = context;
            _logger = logger;
        }

        [HttpPost]
        public async Task<IActionResult> CreateTodo([FromBody] CreateTodoDto createTodoDto, [FromQuery] int userId)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    return BadRequest(new { message = "Kullanıcı bulunamadı." });
                }

                var todoItem = new TodoItem
                {
                    UserId = userId,
                    Title = createTodoDto.Title,
                    Description = createTodoDto.Description,
                    Priority = createTodoDto.Priority,
                    DueDate = createTodoDto.DueDate
                };

                _context.TodoItems.Add(todoItem);
                await _context.SaveChangesAsync();

                return Ok(new { message = "Yapılacak başarıyla oluşturuldu.", todoId = todoItem.Id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while creating todo");
                return StatusCode(500, new { message = "Yapılacak oluşturulurken bir hata oluştu." });
            }
        }

        [HttpGet("user/{userId}")]
        public async Task<IActionResult> GetUserTodos(int userId)
        {
            try
            {
                var todos = await _context.TodoItems
                    .Where(t => t.UserId == userId)
                    .OrderByDescending(t => t.CreatedAt)
                    .Select(t => new
                    {
                        id = t.Id,
                        title = t.Title,
                        description = t.Description,
                        isCompleted = t.IsCompleted,
                        priority = t.Priority,
                        dueDate = t.DueDate,
                        createdAt = t.CreatedAt
                    })
                    .ToListAsync();

                return Ok(todos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting user todos");
                return StatusCode(500, new { message = "Yapılacaklar getirilirken bir hata oluştu." });
            }
        }

        [HttpPut("{id}/complete")]
        public async Task<IActionResult> ToggleTodoComplete(int id, [FromQuery] int userId)
        {
            try
            {
                var todo = await _context.TodoItems
                    .FirstOrDefaultAsync(t => t.Id == id && t.UserId == userId);

                if (todo == null)
                {
                    return NotFound(new { message = "Yapılacak bulunamadı." });
                }

                todo.IsCompleted = !todo.IsCompleted;
                todo.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();

                return Ok(new { message = "Yapılacak durumu güncellendi.", isCompleted = todo.IsCompleted });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while updating todo");
                return StatusCode(500, new { message = "Yapılacak güncellenirken bir hata oluştu." });
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteTodo(int id, [FromQuery] int userId)
        {
            try
            {
                var todo = await _context.TodoItems
                    .FirstOrDefaultAsync(t => t.Id == id && t.UserId == userId);

                if (todo == null)
                {
                    return NotFound(new { message = "Yapılacak bulunamadı." });
                }

                _context.TodoItems.Remove(todo);
                await _context.SaveChangesAsync();

                return Ok(new { message = "Yapılacak başarıyla silindi." });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while deleting todo");
                return StatusCode(500, new { message = "Yapılacak silinirken bir hata oluştu." });
            }
        }

        // Admin için tüm yapılacakları görme
        [HttpGet("admin/all")]
        public async Task<IActionResult> GetAllTodosForAdmin()
        {
            try
            {
                var todos = await _context.TodoItems
                    .Include(t => t.User)
                    .OrderByDescending(t => t.CreatedAt)
                    .Select(t => new
                    {
                        id = t.Id,
                        title = t.Title,
                        description = t.Description,
                        isCompleted = t.IsCompleted,
                        priority = t.Priority,
                        dueDate = t.DueDate,
                        createdAt = t.CreatedAt,
                        user = new
                        {
                            id = t.User.Id,
                            name = $"{t.User.FirstName} {t.User.LastName}",
                            employeeNumber = t.User.EmployeeNumber,
                            department = t.User.Department
                        }
                    })
                    .ToListAsync();

                return Ok(todos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting all todos for admin");
                return StatusCode(500, new { message = "Yapılacaklar getirilirken bir hata oluştu." });
            }
        }
    }
}
