using System.ComponentModel.DataAnnotations;

namespace TalepYonetimAPI.Models.DTOs
{
    public class CreateAnnouncementDto
    {
        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;
        
        [Required]
        public string Content { get; set; } = string.Empty;
        
        [Required]
        [StringLength(20)]
        public string Type { get; set; } = string.Empty; // 'Announcement' veya 'News'
        
        public bool IsImportant { get; set; } = false;
    }
}
