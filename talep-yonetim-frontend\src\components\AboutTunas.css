.about-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  padding: 1rem;
}

.about-modal {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.about-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  background: linear-gradient(135deg, var(--tunas-primary) 0%, var(--tunas-secondary) 100%);
  color: white;
  border-radius: 16px 16px 0 0;
}

.about-logo {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.about-logo .logo-placeholder {
  background: white;
  color: var(--tunas-primary);
  font-weight: bold;
  font-size: 1.8rem;
  padding: 1rem 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.about-logo h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.about-logo p {
  margin: 0;
  opacity: 0.9;
  font-size: 1rem;
}

.close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: rotate(90deg);
}

.about-content {
  padding: 2rem;
}

.hero-section {
  text-align: center;
  margin-bottom: 3rem;
  padding: 2rem;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 12px;
  border-left: 4px solid var(--tunas-accent);
}

.hero-text h3 {
  color: var(--tunas-primary);
  margin-bottom: 1rem;
  font-size: 1.8rem;
}

.hero-text p {
  color: var(--tunas-gray);
  font-size: 1.1rem;
  line-height: 1.6;
  max-width: 700px;
  margin: 0 auto;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  text-align: center;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(30, 58, 138, 0.15);
  border-color: var(--tunas-accent);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.feature-card h4 {
  color: var(--tunas-primary);
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.feature-card p {
  color: var(--tunas-gray);
  line-height: 1.5;
}

.mission-vision {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 3rem;
}

.mission,
.vision {
  padding: 2rem;
  border-radius: 12px;
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
}

.mission h4,
.vision h4 {
  color: var(--tunas-primary);
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.mission p,
.vision p {
  color: var(--tunas-dark);
  line-height: 1.6;
  margin: 0;
}

.values-section {
  margin-bottom: 3rem;
}

.values-section h4 {
  color: var(--tunas-primary);
  margin-bottom: 1.5rem;
  font-size: 1.4rem;
  text-align: center;
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.value-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border-radius: 12px;
  border: 1px solid var(--tunas-accent);
  transition: all 0.3s ease;
}

.value-item:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 20px rgba(16, 185, 129, 0.2);
}

.value-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.value-item span:last-child {
  color: var(--tunas-dark);
  font-weight: 500;
  text-align: center;
}

.achievements {
  margin-bottom: 3rem;
}

.achievements h4 {
  color: var(--tunas-primary);
  margin-bottom: 1.5rem;
  font-size: 1.4rem;
  text-align: center;
}

.achievement-list {
  display: grid;
  gap: 1rem;
}

.achievement-item {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #fef7ff 0%, #fae8ff 100%);
  border-radius: 12px;
  border-left: 4px solid var(--tunas-orange);
}

.achievement-year {
  background: var(--tunas-orange);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: bold;
  min-width: 60px;
  text-align: center;
}

.achievement-text {
  color: var(--tunas-dark);
  font-weight: 500;
}

.contact-cta {
  text-align: center;
  padding: 2rem;
  background: linear-gradient(135deg, var(--tunas-primary) 0%, var(--tunas-secondary) 100%);
  color: white;
  border-radius: 12px;
}

.contact-cta h4 {
  margin-bottom: 1rem;
  font-size: 1.4rem;
}

.contact-cta p {
  margin-bottom: 2rem;
  opacity: 0.9;
}

.cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-btn {
  padding: 1rem 2rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.cta-btn.primary {
  background: var(--tunas-accent);
  color: white;
}

.cta-btn.primary:hover {
  background: #059669;
  transform: translateY(-2px);
}

.cta-btn.secondary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.cta-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

/* Responsive */
@media (max-width: 768px) {
  .about-modal {
    margin: 0.5rem;
    max-height: 95vh;
  }

  .about-header {
    padding: 1.5rem;
  }

  .about-logo {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .about-content {
    padding: 1.5rem;
  }

  .mission-vision {
    grid-template-columns: 1fr;
  }

  .values-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }
}
