import React, { useState, useEffect } from 'react';
import { eventsAPI } from '../services/api';
import './EventCalendar.css';

const EventCalendar = () => {
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [viewMode, setViewMode] = useState('month'); // 'month' or 'list'

  useEffect(() => {
    fetchEvents();
  }, [currentDate]);

  const fetchEvents = async () => {
    try {
      setLoading(true);
      const year = currentDate.getFullYear();
      const month = currentDate.getMonth() + 1;
      const response = await eventsAPI.getCalendar(year, month);
      setEvents(response.data);
    } catch (error) {
      setError('Etkinlikler yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatTime = (dateString) => {
    return new Date(dateString).toLocaleTimeString('tr-TR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getEventTypeIcon = (eventType) => {
    switch (eventType) {
      case 'Meeting': return '🤝';
      case 'Training': return '📚';
      case 'Conference': return '🎤';
      case 'Holiday': return '🏖️';
      default: return '📅';
    }
  };

  const getEventTypeText = (eventType) => {
    switch (eventType) {
      case 'Meeting': return 'Toplantı';
      case 'Training': return 'Eğitim';
      case 'Conference': return 'Konferans';
      case 'Holiday': return 'Tatil';
      default: return 'Etkinlik';
    }
  };

  const navigateMonth = (direction) => {
    const newDate = new Date(currentDate);
    newDate.setMonth(currentDate.getMonth() + direction);
    setCurrentDate(newDate);
  };

  const goToToday = () => {
    setCurrentDate(new Date());
  };

  const getMonthName = () => {
    return currentDate.toLocaleDateString('tr-TR', { 
      year: 'numeric', 
      month: 'long' 
    });
  };

  const upcomingEvents = events
    .filter(event => new Date(event.start) >= new Date())
    .sort((a, b) => new Date(a.start) - new Date(b.start))
    .slice(0, 5);

  if (loading) {
    return (
      <div className="calendar-container">
        <div className="loading">Etkinlik takvimi yükleniyor...</div>
      </div>
    );
  }

  return (
    <div className="calendar-container">
      <div className="calendar-header">
        <h2>📅 Etkinlik Takvimi</h2>
        <p>Şirket etkinliklerini ve önemli tarihleri takip edin.</p>
      </div>

      {error && <div className="error-message">{error}</div>}

      <div className="calendar-controls">
        <div className="month-navigation">
          <button onClick={() => navigateMonth(-1)} className="nav-btn">
            ← Önceki Ay
          </button>
          <h3 className="current-month">{getMonthName()}</h3>
          <button onClick={() => navigateMonth(1)} className="nav-btn">
            Sonraki Ay →
          </button>
        </div>
        
        <div className="view-controls">
          <button onClick={goToToday} className="today-btn">
            📍 Bugün
          </button>
          <div className="view-toggle">
            <button 
              className={`toggle-btn ${viewMode === 'month' ? 'active' : ''}`}
              onClick={() => setViewMode('month')}
            >
              📅 Takvim
            </button>
            <button 
              className={`toggle-btn ${viewMode === 'list' ? 'active' : ''}`}
              onClick={() => setViewMode('list')}
            >
              📋 Liste
            </button>
          </div>
        </div>
      </div>

      {viewMode === 'month' ? (
        <div className="calendar-view">
          <div className="calendar-stats">
            <div className="stat-item">
              <span className="stat-number">{events.length}</span>
              <span className="stat-label">Bu Ay</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">{upcomingEvents.length}</span>
              <span className="stat-label">Yaklaşan</span>
            </div>
          </div>

          {events.length === 0 ? (
            <div className="no-events">
              <div className="no-events-icon">📅</div>
              <h3>Bu ay etkinlik yok</h3>
              <p>Seçili ay için herhangi bir etkinlik bulunmuyor.</p>
            </div>
          ) : (
            <div className="events-grid">
              {events.map(event => (
                <div 
                  key={event.id} 
                  className="event-card"
                  style={{ borderLeftColor: event.color }}
                  onClick={() => setSelectedEvent(event)}
                >
                  <div className="event-header">
                    <div className="event-type">
                      {getEventTypeIcon(event.eventType)}
                      <span>{getEventTypeText(event.eventType)}</span>
                    </div>
                    <div className="event-date">
                      {new Date(event.start).getDate()}
                    </div>
                  </div>
                  
                  <div className="event-content">
                    <h4>{event.title}</h4>
                    {event.description && (
                      <p>{event.description.substring(0, 100)}...</p>
                    )}
                    
                    <div className="event-details">
                      <div className="event-time">
                        🕐 {event.allDay ? 'Tüm Gün' : `${formatTime(event.start)} - ${formatTime(event.end)}`}
                      </div>
                      {event.location && (
                        <div className="event-location">
                          📍 {event.location}
                        </div>
                      )}
                      <div className="event-admin">
                        👤 {event.adminName}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      ) : (
        <div className="list-view">
          <h3>📋 Etkinlik Listesi</h3>
          {events.length === 0 ? (
            <div className="no-events">
              <div className="no-events-icon">📋</div>
              <h3>Bu ay etkinlik yok</h3>
              <p>Seçili ay için herhangi bir etkinlik bulunmuyor.</p>
            </div>
          ) : (
            <div className="events-list">
              {events
                .sort((a, b) => new Date(a.start) - new Date(b.start))
                .map(event => (
                  <div 
                    key={event.id} 
                    className="event-list-item"
                    onClick={() => setSelectedEvent(event)}
                  >
                    <div className="event-list-date">
                      <div className="date-day">
                        {new Date(event.start).getDate()}
                      </div>
                      <div className="date-month">
                        {new Date(event.start).toLocaleDateString('tr-TR', { month: 'short' })}
                      </div>
                    </div>
                    
                    <div className="event-list-content">
                      <div className="event-list-header">
                        <h4>{event.title}</h4>
                        <div className="event-list-type">
                          {getEventTypeIcon(event.eventType)} {getEventTypeText(event.eventType)}
                        </div>
                      </div>
                      
                      <div className="event-list-details">
                        <span className="event-time">
                          🕐 {event.allDay ? 'Tüm Gün' : `${formatTime(event.start)} - ${formatTime(event.end)}`}
                        </span>
                        {event.location && (
                          <span className="event-location">📍 {event.location}</span>
                        )}
                        <span className="event-admin">👤 {event.adminName}</span>
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          )}
        </div>
      )}

      {upcomingEvents.length > 0 && (
        <div className="upcoming-events">
          <h3>🔜 Yaklaşan Etkinlikler</h3>
          <div className="upcoming-list">
            {upcomingEvents.map(event => (
              <div key={event.id} className="upcoming-item">
                <div className="upcoming-date">
                  {formatDate(event.start)}
                </div>
                <div className="upcoming-title">
                  {getEventTypeIcon(event.eventType)} {event.title}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {selectedEvent && (
        <div className="event-modal">
          <div className="modal-content">
            <div className="modal-header">
              <div className="modal-title">
                <div className="modal-type">
                  {getEventTypeIcon(selectedEvent.eventType)} {getEventTypeText(selectedEvent.eventType)}
                </div>
                <h3>{selectedEvent.title}</h3>
              </div>
              <button 
                onClick={() => setSelectedEvent(null)}
                className="close-btn"
              >
                ✕
              </button>
            </div>
            
            <div className="modal-details">
              <div className="detail-item">
                <span className="detail-label">📅 Tarih:</span>
                <span>{formatDate(selectedEvent.start)}</span>
              </div>
              
              {!selectedEvent.allDay && (
                <div className="detail-item">
                  <span className="detail-label">🕐 Saat:</span>
                  <span>{formatTime(selectedEvent.start)} - {formatTime(selectedEvent.end)}</span>
                </div>
              )}
              
              {selectedEvent.location && (
                <div className="detail-item">
                  <span className="detail-label">📍 Konum:</span>
                  <span>{selectedEvent.location}</span>
                </div>
              )}
              
              <div className="detail-item">
                <span className="detail-label">👤 Organizatör:</span>
                <span>{selectedEvent.adminName}</span>
              </div>
            </div>

            {selectedEvent.description && (
              <div className="modal-description">
                <h4>📝 Açıklama:</h4>
                <p>{selectedEvent.description}</p>
              </div>
            )}

            <div className="modal-actions">
              <button 
                onClick={() => setSelectedEvent(null)}
                className="close-modal-btn"
              >
                Kapat
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EventCalendar;
