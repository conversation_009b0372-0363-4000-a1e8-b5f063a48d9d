using System.ComponentModel.DataAnnotations;

namespace TalepYonetimAPI.Models
{
    public class TodoItem
    {
        public int Id { get; set; }
        
        public int UserId { get; set; }
        
        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;
        
        public string? Description { get; set; }
        
        public bool IsCompleted { get; set; } = false;
        
        [StringLength(20)]
        public string Priority { get; set; } = "Normal"; // 'Low', 'Normal', 'High', 'Urgent'
        
        public DateTime? DueDate { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
        
        // Navigation properties
        public virtual User User { get; set; } = null!;
    }
}
