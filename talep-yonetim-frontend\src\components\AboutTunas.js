import React from 'react';
import './AboutTunas.css';

const AboutTunas = ({ onClose }) => {
  return (
    <div className="about-overlay">
      <div className="about-modal">
        <div className="about-header">
          <div className="about-logo">
            <div className="logo-placeholder">TÜNAŞ</div>
            <div>
              <h2>TÜRKİYE NÜKLEER ENERJİ A.Ş.</h2>
              <p>Türkiye'nin Enerji Geleceği</p>
            </div>
          </div>
          <button onClick={onClose} className="close-btn">&times;</button>
        </div>

        <div className="about-content">
          <div className="hero-section">
            <div className="hero-text">
              <h3>🚀 Geleceğin Enerjisi, Bugünün Teknolojisi</h3>
              <p>
                TÜNAŞ, Türkiye'nin nükleer enerji alanındaki öncü kuruluşu olarak, 
                sürdürülebilir ve güvenli enerji çözümleri geliştirmektedir. 
                25 yılı aşkın deneyimimizle, ülkemizin enerji bağımsızlığına katkı sağlıyoruz.
              </p>
            </div>
          </div>

          <div className="features-grid">
            <div className="feature-card">
              <div className="feature-icon">🔬</div>
              <h4>Araştırma & Geliştirme</h4>
              <p>En son teknolojilerle nükleer enerji alanında yenilikçi çözümler üretiyoruz.</p>
            </div>
            
            <div className="feature-card">
              <div className="feature-icon">🛡️</div>
              <h4>Güvenlik Önceliği</h4>
              <p>Uluslararası standartlarda güvenlik protokolleri ile çalışıyoruz.</p>
            </div>
            
            <div className="feature-card">
              <div className="feature-icon">🌱</div>
              <h4>Çevre Dostu</h4>
              <p>Sürdürülebilir enerji üretimi ile çevreyi koruyoruz.</p>
            </div>
            
            <div className="feature-card">
              <div className="feature-icon">🎓</div>
              <h4>Eğitim & Geliştirme</h4>
              <p>Uzman personel yetiştirme ve sürekli eğitim programları.</p>
            </div>
          </div>

          <div className="mission-vision">
            <div className="mission">
              <h4>🎯 Misyonumuz</h4>
              <p>
                Türkiye'nin enerji ihtiyaçlarını karşılamak için güvenli, temiz ve 
                sürdürülebilir nükleer enerji teknolojileri geliştirmek ve uygulamak.
              </p>
            </div>
            
            <div className="vision">
              <h4>🔮 Vizyonumuz</h4>
              <p>
                Nükleer enerji alanında dünya çapında tanınan, teknoloji lideri bir 
                kuruluş olmak ve Türkiye'yi enerji bağımsızlığına kavuşturmak.
              </p>
            </div>
          </div>

          <div className="values-section">
            <h4>💎 Değerlerimiz</h4>
            <div className="values-grid">
              <div className="value-item">
                <span className="value-icon">🤝</span>
                <span>Güvenilirlik</span>
              </div>
              <div className="value-item">
                <span className="value-icon">🎯</span>
                <span>Mükemmellik</span>
              </div>
              <div className="value-item">
                <span className="value-icon">🌟</span>
                <span>İnovasyon</span>
              </div>
              <div className="value-item">
                <span className="value-icon">🤲</span>
                <span>Sorumluluk</span>
              </div>
              <div className="value-item">
                <span className="value-icon">🔄</span>
                <span>Sürdürülebilirlik</span>
              </div>
              <div className="value-item">
                <span className="value-icon">👥</span>
                <span>Takım Çalışması</span>
              </div>
            </div>
          </div>

          <div className="achievements">
            <h4>🏆 Başarılarımız</h4>
            <div className="achievement-list">
              <div className="achievement-item">
                <span className="achievement-year">2023</span>
                <span className="achievement-text">Yenilenebilir Enerji Ödülü</span>
              </div>
              <div className="achievement-item">
                <span className="achievement-year">2022</span>
                <span className="achievement-text">En İyi Ar-Ge Projesi Ödülü</span>
              </div>
              <div className="achievement-item">
                <span className="achievement-year">2021</span>
                <span className="achievement-text">Teknoloji İnovasyon Ödülü</span>
              </div>
              <div className="achievement-item">
                <span className="achievement-year">2020</span>
                <span className="achievement-text">Çevre Dostu Şirket Sertifikası</span>
              </div>
            </div>
          </div>

          <div className="contact-cta">
            <h4>📞 Bizimle İletişime Geçin</h4>
            <p>Sorularınız için her zaman yanınızdayız.</p>
            <div className="cta-buttons">
              <a href="mailto:<EMAIL>" className="cta-btn primary">
                📧 E-posta Gönder
              </a>
              <a href="tel:+903122850021" className="cta-btn secondary">
                📞 Hemen Ara
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AboutTunas;
