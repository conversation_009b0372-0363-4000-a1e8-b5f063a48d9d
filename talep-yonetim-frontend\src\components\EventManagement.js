import React, { useState, useEffect } from 'react';
import { eventsAPI } from '../services/api';
import { useAuth } from '../context/AuthContext';
import './EventManagement.css';

const EventManagement = () => {
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingEvent, setEditingEvent] = useState(null);
  const [filterType, setFilterType] = useState('all'); // 'all', 'upcoming', 'past'
  const [newEvent, setNewEvent] = useState({
    title: '',
    description: '',
    eventDate: '',
    location: '',
    isImportant: false
  });
  const { user } = useAuth();

  useEffect(() => {
    fetchEvents();
  }, []);

  const fetchEvents = async () => {
    try {
      setLoading(true);
      const response = await eventsAPI.getAll();
      setEvents(response.data);
    } catch (error) {
      setError('Etkinlikler yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = async (e) => {
    e.preventDefault();
    try {
      await eventsAPI.create(newEvent, user.id);
      setSuccess('Etkinlik başarıyla oluşturuldu!');
      setNewEvent({
        title: '',
        description: '',
        eventDate: '',
        location: '',
        isImportant: false
      });
      setShowCreateForm(false);
      fetchEvents();
      setTimeout(() => setSuccess(''), 3000);
    } catch (error) {
      setError('Etkinlik oluşturulurken bir hata oluştu.');
    }
  };

  const handleUpdate = async (e) => {
    e.preventDefault();
    try {
      await eventsAPI.update(editingEvent.id, newEvent, user.id);
      setSuccess('Etkinlik başarıyla güncellendi!');
      setEditingEvent(null);
      setNewEvent({
        title: '',
        description: '',
        eventDate: '',
        location: '',
        isImportant: false
      });
      fetchEvents();
      setTimeout(() => setSuccess(''), 3000);
    } catch (error) {
      setError('Etkinlik güncellenirken bir hata oluştu.');
    }
  };

  const handleDelete = async (id, title) => {
    if (!window.confirm(`"${title}" etkinliğini silmek istediğinizden emin misiniz?`)) {
      return;
    }
    
    try {
      await eventsAPI.delete(id, user.id);
      setSuccess('Etkinlik başarıyla silindi!');
      fetchEvents();
      setTimeout(() => setSuccess(''), 3000);
    } catch (error) {
      setError('Etkinlik silinirken bir hata oluştu.');
    }
  };

  const startEdit = (event) => {
    setEditingEvent(event);
    setNewEvent({
      title: event.title,
      description: event.description,
      eventDate: new Date(event.eventDate).toISOString().slice(0, 16),
      location: event.location,
      isImportant: event.isImportant
    });
    setShowCreateForm(true);
  };

  const cancelEdit = () => {
    setEditingEvent(null);
    setNewEvent({
      title: '',
      description: '',
      eventDate: '',
      location: '',
      isImportant: false
    });
    setShowCreateForm(false);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const isUpcoming = (eventDate) => {
    return new Date(eventDate) > new Date();
  };

  const isPast = (eventDate) => {
    return new Date(eventDate) < new Date();
  };

  const filteredEvents = events.filter(event => {
    switch (filterType) {
      case 'upcoming':
        return isUpcoming(event.eventDate);
      case 'past':
        return isPast(event.eventDate);
      default:
        return true;
    }
  });

  const upcomingCount = events.filter(event => isUpcoming(event.eventDate)).length;
  const pastCount = events.filter(event => isPast(event.eventDate)).length;
  const importantCount = events.filter(event => event.isImportant).length;

  if (loading) {
    return (
      <div className="event-management-container">
        <div className="loading">Etkinlikler yükleniyor...</div>
      </div>
    );
  }

  return (
    <div className="event-management-container">
      <div className="event-management-header">
        <h2>📅 Etkinlik Yönetimi</h2>
        <p>Şirket etkinliklerini oluşturun, düzenleyin ve yönetin.</p>
        <button 
          onClick={() => setShowCreateForm(!showCreateForm)}
          className="create-event-btn"
        >
          ➕ Yeni Etkinlik {editingEvent ? 'Düzenle' : 'Oluştur'}
        </button>
      </div>

      {error && <div className="error-message">{error}</div>}
      {success && <div className="success-message">{success}</div>}

      <div className="stats-section">
        <div className="stat-card">
          <div className="stat-number">{events.length}</div>
          <div className="stat-label">Toplam Etkinlik</div>
        </div>
        <div className="stat-card upcoming">
          <div className="stat-number">{upcomingCount}</div>
          <div className="stat-label">Yaklaşan</div>
        </div>
        <div className="stat-card past">
          <div className="stat-number">{pastCount}</div>
          <div className="stat-label">Geçmiş</div>
        </div>
        <div className="stat-card important">
          <div className="stat-number">{importantCount}</div>
          <div className="stat-label">Önemli</div>
        </div>
      </div>

      {showCreateForm && (
        <div className="create-form">
          <h3>{editingEvent ? 'Etkinlik Düzenle' : 'Yeni Etkinlik Oluştur'}</h3>
          <form onSubmit={editingEvent ? handleUpdate : handleCreate}>
            <div className="form-row">
              <div className="form-group">
                <label>Etkinlik Başlığı *</label>
                <input
                  type="text"
                  value={newEvent.title}
                  onChange={(e) => setNewEvent({...newEvent, title: e.target.value})}
                  placeholder="Etkinlik başlığı girin..."
                  required
                />
              </div>
              
              <div className="form-group">
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={newEvent.isImportant}
                    onChange={(e) => setNewEvent({...newEvent, isImportant: e.target.checked})}
                  />
                  ⚠️ Önemli etkinlik olarak işaretle
                </label>
              </div>
            </div>
            
            <div className="form-row">
              <div className="form-group">
                <label>Etkinlik Tarihi ve Saati *</label>
                <input
                  type="datetime-local"
                  value={newEvent.eventDate}
                  onChange={(e) => setNewEvent({...newEvent, eventDate: e.target.value})}
                  required
                />
              </div>
              
              <div className="form-group">
                <label>Konum</label>
                <input
                  type="text"
                  value={newEvent.location}
                  onChange={(e) => setNewEvent({...newEvent, location: e.target.value})}
                  placeholder="Etkinlik konumu..."
                />
              </div>
            </div>
            
            <div className="form-group">
              <label>Açıklama</label>
              <textarea
                value={newEvent.description}
                onChange={(e) => setNewEvent({...newEvent, description: e.target.value})}
                placeholder="Etkinlik hakkında detaylar..."
                rows="5"
              />
            </div>
            
            <div className="form-actions">
              <button type="submit" className="submit-btn">
                {editingEvent ? 'Güncelle' : 'Oluştur'}
              </button>
              <button 
                type="button" 
                onClick={cancelEdit}
                className="cancel-btn"
              >
                İptal
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="filter-section">
        <div className="filter-group">
          <label>Filtrele:</label>
          <select 
            value={filterType} 
            onChange={(e) => setFilterType(e.target.value)}
            className="filter-select"
          >
            <option value="all">Tüm Etkinlikler ({events.length})</option>
            <option value="upcoming">📅 Yaklaşan ({upcomingCount})</option>
            <option value="past">📋 Geçmiş ({pastCount})</option>
          </select>
        </div>
      </div>

      {filteredEvents.length === 0 ? (
        <div className="no-events">
          <div className="no-events-icon">📅</div>
          <h3>Etkinlik bulunamadı</h3>
          <p>Seçili filtreye uygun etkinlik bulunmuyor.</p>
        </div>
      ) : (
        <div className="events-list">
          {filteredEvents.map(event => (
            <div key={event.id} className={`event-card ${isPast(event.eventDate) ? 'past' : 'upcoming'} ${event.isImportant ? 'important' : ''}`}>
              <div className="event-header">
                <div className="event-status">
                  {isUpcoming(event.eventDate) ? (
                    <span className="status upcoming">📅 Yaklaşan</span>
                  ) : (
                    <span className="status past">📋 Geçmiş</span>
                  )}
                  {event.isImportant && <span className="important-badge">⚠️ Önemli</span>}
                </div>
                <div className="event-actions">
                  <button 
                    onClick={() => startEdit(event)}
                    className="edit-btn"
                    title="Düzenle"
                  >
                    ✏️
                  </button>
                  <button 
                    onClick={() => handleDelete(event.id, event.title)}
                    className="delete-btn"
                    title="Sil"
                  >
                    🗑️
                  </button>
                </div>
              </div>
              
              <div className="event-content">
                <h4>{event.title}</h4>
                <div className="event-details">
                  <div className="detail-item">
                    <span className="detail-icon">📅</span>
                    <span className="detail-text">{formatDate(event.eventDate)}</span>
                  </div>
                  {event.location && (
                    <div className="detail-item">
                      <span className="detail-icon">📍</span>
                      <span className="detail-text">{event.location}</span>
                    </div>
                  )}
                </div>
                {event.description && (
                  <div className="event-description">
                    <p>{event.description}</p>
                  </div>
                )}
              </div>
              
              <div className="event-meta">
                <span className="created-date">📅 Oluşturulma: {formatDate(event.createdAt)}</span>
                {event.updatedAt !== event.createdAt && (
                  <span className="updated-date">✏️ Güncellendi: {formatDate(event.updatedAt)}</span>
                )}
                <span className="admin-name">👤 {event.adminName}</span>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default EventManagement;
