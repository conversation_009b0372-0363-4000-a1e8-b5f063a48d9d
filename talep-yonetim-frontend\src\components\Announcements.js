import React, { useState, useEffect } from 'react';
import { announcementsAPI } from '../services/api';
import './Announcements.css';

const Announcements = () => {
  const [announcements, setAnnouncements] = useState([]);
  const [news, setNews] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('announcements');
  const [selectedItem, setSelectedItem] = useState(null);

  useEffect(() => {
    fetchAnnouncements();
    fetchNews();
  }, []);

  const fetchAnnouncements = async () => {
    try {
      setLoading(true);
      const response = await announcementsAPI.getAll('Announcement');
      setAnnouncements(response.data);
    } catch (error) {
      setError('Duyurular yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const fetchNews = async () => {
    try {
      const response = await announcementsAPI.getAll('News');
      setNews(response.data);
    } catch (error) {
      console.error('Haberler yüklenirken hata:', error);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const truncateContent = (content, maxLength = 150) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  if (loading) {
    return (
      <div className="announcements-container">
        <div className="loading">Duyurular yükleniyor...</div>
      </div>
    );
  }

  const currentData = activeTab === 'announcements' ? announcements : news;

  return (
    <div className="announcements-container">
      <div className="announcements-header">
        <h2>📰 Duyurular & Haberler</h2>
        <p>Şirket duyurularını ve güncel haberleri takip edin.</p>
      </div>

      {error && <div className="error-message">{error}</div>}

      <div className="announcement-tabs">
        <button 
          className={`tab-btn ${activeTab === 'announcements' ? 'active' : ''}`}
          onClick={() => setActiveTab('announcements')}
        >
          📢 Duyurular ({announcements.length})
        </button>
        <button 
          className={`tab-btn ${activeTab === 'news' ? 'active' : ''}`}
          onClick={() => setActiveTab('news')}
        >
          📰 Haberler ({news.length})
        </button>
      </div>

      {currentData.length === 0 ? (
        <div className="no-announcements">
          <div className="no-announcements-icon">
            {activeTab === 'announcements' ? '📢' : '📰'}
          </div>
          <h3>
            {activeTab === 'announcements' ? 'Henüz duyuru yok' : 'Henüz haber yok'}
          </h3>
          <p>
            {activeTab === 'announcements' 
              ? 'Yeni duyurular eklendiğinde burada görünecek.' 
              : 'Yeni haberler eklendiğinde burada görünecek.'
            }
          </p>
        </div>
      ) : (
        <div className="announcements-grid">
          {currentData.map(item => (
            <div 
              key={item.id} 
              className={`announcement-card ${item.isImportant ? 'important' : ''}`}
            >
              <div className="announcement-header">
                <div className="announcement-type">
                  {activeTab === 'announcements' ? '📢' : '📰'}
                  <span>{activeTab === 'announcements' ? 'Duyuru' : 'Haber'}</span>
                  {item.isImportant && <span className="important-badge">⚠️ Önemli</span>}
                </div>
                <div className="announcement-date">
                  📅 {formatDate(item.createdAt)}
                </div>
              </div>

              <div className="announcement-content">
                <h3>{item.title}</h3>
                <p>{truncateContent(item.content)}</p>
                
                <div className="announcement-meta">
                  <span className="admin-name">👤 {item.adminName}</span>
                  {item.updatedAt !== item.createdAt && (
                    <span className="updated-date">
                      ✏️ Güncellendi: {formatDate(item.updatedAt)}
                    </span>
                  )}
                </div>
              </div>

              <div className="announcement-actions">
                <button 
                  onClick={() => setSelectedItem(item)}
                  className="read-more-btn"
                >
                  📖 Devamını Oku
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {selectedItem && (
        <div className="announcement-modal">
          <div className="modal-content">
            <div className="modal-header">
              <div className="modal-title">
                <div className="modal-type">
                  {selectedItem.type === 'Announcement' ? '📢 Duyuru' : '📰 Haber'}
                  {selectedItem.isImportant && <span className="important-badge">⚠️ Önemli</span>}
                </div>
                <h3>{selectedItem.title}</h3>
              </div>
              <button 
                onClick={() => setSelectedItem(null)}
                className="close-btn"
              >
                ✕
              </button>
            </div>
            
            <div className="modal-meta">
              <div className="meta-item">
                <span className="meta-label">👤 Yayınlayan:</span>
                <span>{selectedItem.adminName}</span>
              </div>
              <div className="meta-item">
                <span className="meta-label">📅 Yayın Tarihi:</span>
                <span>{formatDate(selectedItem.createdAt)}</span>
              </div>
              {selectedItem.updatedAt !== selectedItem.createdAt && (
                <div className="meta-item">
                  <span className="meta-label">✏️ Güncelleme:</span>
                  <span>{formatDate(selectedItem.updatedAt)}</span>
                </div>
              )}
            </div>

            <div className="modal-content-text">
              {selectedItem.content.split('\n').map((paragraph, index) => (
                <p key={index}>{paragraph}</p>
              ))}
            </div>

            <div className="modal-actions">
              <button 
                onClick={() => setSelectedItem(null)}
                className="close-modal-btn"
              >
                Kapat
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Announcements;
