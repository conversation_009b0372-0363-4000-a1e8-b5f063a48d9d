.favorites-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.favorites-header {
  text-align: center;
  margin-bottom: 2rem;
  padding: 2rem;
  background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
  border-radius: 16px;
  border-left: 5px solid var(--tunas-orange);
}

.favorites-header h2 {
  margin: 0 0 0.5rem 0;
  color: var(--tunas-primary);
  font-size: 2rem;
}

.favorites-header p {
  margin: 0;
  color: var(--tunas-gray);
  font-size: 1.1rem;
}

.loading {
  text-align: center;
  padding: 3rem;
  font-size: 1.2rem;
  color: var(--tunas-gray);
}

.error-message {
  background-color: #fef2f2;
  color: #dc2626;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  border-left: 4px solid #dc2626;
}

.no-favorites {
  text-align: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.no-favorites-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-favorites h3 {
  color: var(--tunas-primary);
  margin-bottom: 1rem;
}

.no-favorites p {
  color: var(--tunas-gray);
  max-width: 400px;
  margin: 0 auto;
  line-height: 1.6;
}

.favorites-list {
  display: grid;
  gap: 1.5rem;
}

.favorite-card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border-left: 5px solid var(--tunas-orange);
  transition: all 0.3s ease;
}

.favorite-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.favorite-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
}

.favorite-info {
  flex: 1;
}

.favorite-info h3 {
  margin: 0 0 0.5rem 0;
  color: var(--tunas-primary);
  font-size: 1.3rem;
}

.favorite-meta {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.added-date {
  color: var(--tunas-gray);
  font-size: 0.9rem;
}

.status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
}

.status.pending {
  background-color: #fef3c7;
  color: #92400e;
}

.status.answered {
  background-color: #d1fae5;
  color: #065f46;
}

.remove-favorite-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
  opacity: 0.6;
}

.remove-favorite-btn:hover {
  opacity: 1;
  background-color: #fef2f2;
  transform: scale(1.1);
}

.request-content {
  margin-bottom: 1.5rem;
}

.content-section {
  margin-bottom: 1rem;
}

.content-section strong {
  display: block;
  color: var(--tunas-primary);
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.content-section p {
  margin: 0;
  color: var(--tunas-dark);
  line-height: 1.6;
}

.content-section span {
  color: var(--tunas-gray);
}

.ai-summary {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  padding: 1rem;
  border-radius: 8px;
  border-left: 3px solid var(--tunas-secondary);
  font-style: italic;
}

.responses-section {
  border-top: 1px solid #e5e7eb;
  padding-top: 1.5rem;
}

.responses-section h4 {
  margin: 0 0 1rem 0;
  color: var(--tunas-primary);
  font-size: 1.1rem;
}

.response-item {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 1.5rem;
  border-radius: 12px;
  margin-bottom: 1rem;
  border-left: 3px solid var(--tunas-accent);
}

.response-content p {
  margin: 0 0 1rem 0;
  color: var(--tunas-dark);
  line-height: 1.6;
}

.response-meta {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.admin-name,
.response-date {
  color: var(--tunas-gray);
  font-size: 0.9rem;
}

/* Responsive */
@media (max-width: 768px) {
  .favorites-container {
    padding: 1rem 0.5rem;
  }
  
  .favorites-header {
    padding: 1.5rem;
  }
  
  .favorites-header h2 {
    font-size: 1.5rem;
  }
  
  .favorite-card {
    padding: 1.5rem;
  }
  
  .favorite-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .favorite-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .response-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
