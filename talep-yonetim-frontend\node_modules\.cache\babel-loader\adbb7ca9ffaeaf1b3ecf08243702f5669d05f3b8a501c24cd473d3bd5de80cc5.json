{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\metin_kayit\\\\talep-yonetim-frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { AuthProvider, useAuth } from './context/AuthContext';\nimport Login from './components/Login';\nimport Register from './components/Register';\nimport UserDashboard from './components/UserDashboard';\nimport AdminDashboard from './components/AdminDashboard';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction AppContent() {\n  _s();\n  const [showRegister, setShowRegister] = useState(false);\n  const {\n    user,\n    loading\n  } = useAuth();\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading\",\n        children: \"Y\\xFCkleniyor...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this);\n  }\n  if (!user) {\n    return showRegister ? /*#__PURE__*/_jsxDEV(Register, {\n      onSwitchToLogin: () => setShowRegister(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this) : /*#__PURE__*/_jsxDEV(Login, {\n      onSwitchToRegister: () => setShowRegister(true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this);\n  }\n  return user.userType === 'Admin' ? /*#__PURE__*/_jsxDEV(AdminDashboard, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 38\n  }, this) : /*#__PURE__*/_jsxDEV(UserDashboard, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 59\n  }, this);\n}\n_s(AppContent, \"Tjn6RntgPH1BaRWC8On9G6IGRfE=\", false, function () {\n  return [useAuth];\n});\n_c = AppContent;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"App\",\n      children: /*#__PURE__*/_jsxDEV(AppContent, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this);\n}\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"AppContent\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["React", "useState", "<PERSON>th<PERSON><PERSON><PERSON>", "useAuth", "<PERSON><PERSON>", "Register", "UserDashboard", "AdminDashboard", "jsxDEV", "_jsxDEV", "A<PERSON><PERSON><PERSON>nt", "_s", "showRegister", "setShowRegister", "user", "loading", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSwitchToLogin", "onSwitchToRegister", "userType", "_c", "App", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/metin_kayit/talep-yonetim-frontend/src/App.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { AuthProvider, useAuth } from './context/AuthContext';\nimport Login from './components/Login';\nimport Register from './components/Register';\nimport UserDashboard from './components/UserDashboard';\nimport AdminDashboard from './components/AdminDashboard';\nimport './App.css';\n\nfunction AppContent() {\n  const [showRegister, setShowRegister] = useState(false);\n  const { user, loading } = useAuth();\n\n  if (loading) {\n    return (\n      <div className=\"loading-container\">\n        <div className=\"loading\">Yükleniyor...</div>\n      </div>\n    );\n  }\n\n  if (!user) {\n    return showRegister ? (\n      <Register onSwitchToLogin={() => setShowRegister(false)} />\n    ) : (\n      <Login onSwitchToRegister={() => setShowRegister(true)} />\n    );\n  }\n\n  return user.userType === 'Admin' ? <AdminDashboard /> : <UserDashboard />;\n}\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <div className=\"App\">\n        <AppContent />\n      </div>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,YAAY,EAAEC,OAAO,QAAQ,uBAAuB;AAC7D,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,UAAUA,CAAA,EAAG;EAAAC,EAAA;EACpB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM;IAAEa,IAAI;IAAEC;EAAQ,CAAC,GAAGZ,OAAO,CAAC,CAAC;EAEnC,IAAIY,OAAO,EAAE;IACX,oBACEN,OAAA;MAAKO,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChCR,OAAA;QAAKO,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC,CAAC;EAEV;EAEA,IAAI,CAACP,IAAI,EAAE;IACT,OAAOF,YAAY,gBACjBH,OAAA,CAACJ,QAAQ;MAACiB,eAAe,EAAEA,CAAA,KAAMT,eAAe,CAAC,KAAK;IAAE;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAE3DZ,OAAA,CAACL,KAAK;MAACmB,kBAAkB,EAAEA,CAAA,KAAMV,eAAe,CAAC,IAAI;IAAE;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAC1D;EACH;EAEA,OAAOP,IAAI,CAACU,QAAQ,KAAK,OAAO,gBAAGf,OAAA,CAACF,cAAc;IAAAW,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,gBAAGZ,OAAA,CAACH,aAAa;IAAAY,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC3E;AAACV,EAAA,CArBQD,UAAU;EAAA,QAESP,OAAO;AAAA;AAAAsB,EAAA,GAF1Bf,UAAU;AAuBnB,SAASgB,GAAGA,CAAA,EAAG;EACb,oBACEjB,OAAA,CAACP,YAAY;IAAAe,QAAA,eACXR,OAAA;MAAKO,SAAS,EAAC,KAAK;MAAAC,QAAA,eAClBR,OAAA,CAACC,UAAU;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACM,CAAC;AAEnB;AAACM,GAAA,GARQD,GAAG;AAUZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}