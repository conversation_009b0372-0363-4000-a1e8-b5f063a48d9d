using System.ComponentModel.DataAnnotations;

namespace TalepYonetimAPI.Models
{
    public class FavoriteRequest
    {
        public int Id { get; set; }
        
        public int UserId { get; set; }
        
        public int RequestId { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        // Navigation properties
        public virtual User User { get; set; } = null!;
        public virtual Request Request { get; set; } = null!;
    }
}
