{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = 'http://localhost:5199/api';\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Auth API calls\nexport const authAPI = {\n  login: (employeeNumber, password) => api.post('/auth/login', {\n    employeeNumber,\n    password\n  }),\n  register: userData => api.post('/auth/register', userData)\n};\n\n// Requests API calls\nexport const requestsAPI = {\n  create: (requestData, userId) => api.post(`/requests?userId=${userId}`, requestData),\n  getUserRequests: userId => api.get(`/requests/user/${userId}`),\n  getAllForAdmin: () => api.get('/requests/admin')\n};\n\n// Responses API calls\nexport const responsesAPI = {\n  create: (responseData, adminId) => api.post(`/responses?adminId=${adminId}`, responseData),\n  getByRequest: requestId => api.get(`/responses/request/${requestId}`)\n};\n\n// Favori Talepler API\nexport const favoritesAPI = {\n  add: (userId, requestId) => api.post(`/favoriterequests?userId=${userId}&requestId=${requestId}`),\n  remove: (userId, requestId) => api.delete(`/favoriterequests?userId=${userId}&requestId=${requestId}`),\n  getUserFavorites: userId => api.get(`/favoriterequests/user/${userId}`),\n  checkFavorite: (userId, requestId) => api.get(`/favoriterequests/check?userId=${userId}&requestId=${requestId}`)\n};\n\n// Yapılacaklar API\nexport const todoAPI = {\n  create: (data, userId) => api.post(`/todo?userId=${userId}`, data),\n  getUserTodos: userId => api.get(`/todo/user/${userId}`),\n  toggleComplete: (id, userId) => api.put(`/todo/${id}/complete?userId=${userId}`),\n  delete: (id, userId) => api.delete(`/todo/${id}?userId=${userId}`),\n  getAllForAdmin: () => api.get('/todo/admin/all')\n};\n\n// Anketler API\nexport const surveysAPI = {\n  create: (data, adminId) => api.post(`/surveys?adminId=${adminId}`, data),\n  getActive: () => api.get('/surveys/active'),\n  respond: (data, userId) => api.post(`/surveys/respond?userId=${userId}`, data),\n  getResponses: (surveyId, adminId) => api.get(`/surveys/${surveyId}/responses?adminId=${adminId}`),\n  getUserAnswered: userId => api.get(`/surveys/user/${userId}/answered`),\n  checkAnswered: (userId, surveyId) => api.get(`/surveys/check-answered?userId=${userId}&surveyId=${surveyId}`)\n};\n\n// Duyurular API\nexport const announcementsAPI = {\n  create: (data, adminId) => api.post(`/announcements?adminId=${adminId}`, data),\n  getAll: (type = null) => api.get(`/announcements${type ? `?type=${type}` : ''}`),\n  update: (id, data, adminId) => api.put(`/announcements/${id}?adminId=${adminId}`, data),\n  delete: (id, adminId) => api.delete(`/announcements/${id}?adminId=${adminId}`)\n};\n\n// Etkinlikler API\nexport const eventsAPI = {\n  create: (data, adminId) => api.post(`/events?adminId=${adminId}`, data),\n  getAll: (startDate = null, endDate = null) => {\n    let url = '/events';\n    const params = [];\n    if (startDate) params.push(`startDate=${startDate}`);\n    if (endDate) params.push(`endDate=${endDate}`);\n    if (params.length > 0) url += `?${params.join('&')}`;\n    return api.get(url);\n  },\n  getCalendar: (year, month) => api.get(`/events/calendar/${year}/${month}`),\n  update: (id, data, adminId) => api.put(`/events/${id}?adminId=${adminId}`, data),\n  delete: (id, adminId) => api.delete(`/events/${id}?adminId=${adminId}`)\n};\n\n// Profil API\nexport const profileAPI = {\n  get: userId => api.get(`/profile/${userId}`),\n  update: (userId, data) => api.put(`/profile/${userId}`, data),\n  getSettings: userId => api.get(`/profile/${userId}/settings`),\n  updateSettings: (userId, data) => api.put(`/profile/${userId}/settings`, data),\n  getStats: userId => api.get(`/profile/${userId}/stats`)\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "api", "create", "baseURL", "headers", "authAPI", "login", "employeeNumber", "password", "post", "register", "userData", "requestsAPI", "requestData", "userId", "getUserRequests", "get", "getAllForAdmin", "responsesAPI", "responseData", "adminId", "getByRequest", "requestId", "favoritesAPI", "add", "remove", "delete", "getUserFavorites", "checkFavorite", "todoAPI", "data", "getUserTodos", "toggleComplete", "id", "put", "surveysAPI", "getActive", "respond", "getResponses", "surveyId", "getUserAnswered", "checkAnswered", "announcementsAPI", "getAll", "type", "update", "eventsAPI", "startDate", "endDate", "url", "params", "push", "length", "join", "getCalendar", "year", "month", "profileAPI", "getSettings", "updateSettings", "getStats"], "sources": ["C:/Users/<USER>/Desktop/metin_kayit/talep-yonetim-frontend/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = 'http://localhost:5199/api';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Auth API calls\nexport const authAPI = {\n  login: (employeeNumber, password) =>\n    api.post('/auth/login', { employeeNumber, password }),\n  \n  register: (userData) =>\n    api.post('/auth/register', userData),\n};\n\n// Requests API calls\nexport const requestsAPI = {\n  create: (requestData, userId) =>\n    api.post(`/requests?userId=${userId}`, requestData),\n  \n  getUserRequests: (userId) =>\n    api.get(`/requests/user/${userId}`),\n  \n  getAllForAdmin: () =>\n    api.get('/requests/admin'),\n};\n\n// Responses API calls\nexport const responsesAPI = {\n  create: (responseData, adminId) =>\n    api.post(`/responses?adminId=${adminId}`, responseData),\n\n  getByRequest: (requestId) =>\n    api.get(`/responses/request/${requestId}`),\n};\n\n// Favori Talepler API\nexport const favoritesAPI = {\n  add: (userId, requestId) => api.post(`/favoriterequests?userId=${userId}&requestId=${requestId}`),\n  remove: (userId, requestId) => api.delete(`/favoriterequests?userId=${userId}&requestId=${requestId}`),\n  getUserFavorites: (userId) => api.get(`/favoriterequests/user/${userId}`),\n  checkFavorite: (userId, requestId) => api.get(`/favoriterequests/check?userId=${userId}&requestId=${requestId}`)\n};\n\n// Yapılacaklar API\nexport const todoAPI = {\n  create: (data, userId) => api.post(`/todo?userId=${userId}`, data),\n  getUserTodos: (userId) => api.get(`/todo/user/${userId}`),\n  toggleComplete: (id, userId) => api.put(`/todo/${id}/complete?userId=${userId}`),\n  delete: (id, userId) => api.delete(`/todo/${id}?userId=${userId}`),\n  getAllForAdmin: () => api.get('/todo/admin/all')\n};\n\n// Anketler API\nexport const surveysAPI = {\n  create: (data, adminId) => api.post(`/surveys?adminId=${adminId}`, data),\n  getActive: () => api.get('/surveys/active'),\n  respond: (data, userId) => api.post(`/surveys/respond?userId=${userId}`, data),\n  getResponses: (surveyId, adminId) => api.get(`/surveys/${surveyId}/responses?adminId=${adminId}`),\n  getUserAnswered: (userId) => api.get(`/surveys/user/${userId}/answered`),\n  checkAnswered: (userId, surveyId) => api.get(`/surveys/check-answered?userId=${userId}&surveyId=${surveyId}`)\n};\n\n// Duyurular API\nexport const announcementsAPI = {\n  create: (data, adminId) => api.post(`/announcements?adminId=${adminId}`, data),\n  getAll: (type = null) => api.get(`/announcements${type ? `?type=${type}` : ''}`),\n  update: (id, data, adminId) => api.put(`/announcements/${id}?adminId=${adminId}`, data),\n  delete: (id, adminId) => api.delete(`/announcements/${id}?adminId=${adminId}`)\n};\n\n// Etkinlikler API\nexport const eventsAPI = {\n  create: (data, adminId) => api.post(`/events?adminId=${adminId}`, data),\n  getAll: (startDate = null, endDate = null) => {\n    let url = '/events';\n    const params = [];\n    if (startDate) params.push(`startDate=${startDate}`);\n    if (endDate) params.push(`endDate=${endDate}`);\n    if (params.length > 0) url += `?${params.join('&')}`;\n    return api.get(url);\n  },\n  getCalendar: (year, month) => api.get(`/events/calendar/${year}/${month}`),\n  update: (id, data, adminId) => api.put(`/events/${id}?adminId=${adminId}`, data),\n  delete: (id, adminId) => api.delete(`/events/${id}?adminId=${adminId}`)\n};\n\n// Profil API\nexport const profileAPI = {\n  get: (userId) => api.get(`/profile/${userId}`),\n  update: (userId, data) => api.put(`/profile/${userId}`, data),\n  getSettings: (userId) => api.get(`/profile/${userId}/settings`),\n  updateSettings: (userId, data) => api.put(`/profile/${userId}/settings`, data),\n  getStats: (userId) => api.get(`/profile/${userId}/stats`)\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAG,2BAA2B;AAEhD,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC;EACvBC,OAAO,EAAEH,YAAY;EACrBI,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMC,OAAO,GAAG;EACrBC,KAAK,EAAEA,CAACC,cAAc,EAAEC,QAAQ,KAC9BP,GAAG,CAACQ,IAAI,CAAC,aAAa,EAAE;IAAEF,cAAc;IAAEC;EAAS,CAAC,CAAC;EAEvDE,QAAQ,EAAGC,QAAQ,IACjBV,GAAG,CAACQ,IAAI,CAAC,gBAAgB,EAAEE,QAAQ;AACvC,CAAC;;AAED;AACA,OAAO,MAAMC,WAAW,GAAG;EACzBV,MAAM,EAAEA,CAACW,WAAW,EAAEC,MAAM,KAC1Bb,GAAG,CAACQ,IAAI,CAAC,oBAAoBK,MAAM,EAAE,EAAED,WAAW,CAAC;EAErDE,eAAe,EAAGD,MAAM,IACtBb,GAAG,CAACe,GAAG,CAAC,kBAAkBF,MAAM,EAAE,CAAC;EAErCG,cAAc,EAAEA,CAAA,KACdhB,GAAG,CAACe,GAAG,CAAC,iBAAiB;AAC7B,CAAC;;AAED;AACA,OAAO,MAAME,YAAY,GAAG;EAC1BhB,MAAM,EAAEA,CAACiB,YAAY,EAAEC,OAAO,KAC5BnB,GAAG,CAACQ,IAAI,CAAC,sBAAsBW,OAAO,EAAE,EAAED,YAAY,CAAC;EAEzDE,YAAY,EAAGC,SAAS,IACtBrB,GAAG,CAACe,GAAG,CAAC,sBAAsBM,SAAS,EAAE;AAC7C,CAAC;;AAED;AACA,OAAO,MAAMC,YAAY,GAAG;EAC1BC,GAAG,EAAEA,CAACV,MAAM,EAAEQ,SAAS,KAAKrB,GAAG,CAACQ,IAAI,CAAC,4BAA4BK,MAAM,cAAcQ,SAAS,EAAE,CAAC;EACjGG,MAAM,EAAEA,CAACX,MAAM,EAAEQ,SAAS,KAAKrB,GAAG,CAACyB,MAAM,CAAC,4BAA4BZ,MAAM,cAAcQ,SAAS,EAAE,CAAC;EACtGK,gBAAgB,EAAGb,MAAM,IAAKb,GAAG,CAACe,GAAG,CAAC,0BAA0BF,MAAM,EAAE,CAAC;EACzEc,aAAa,EAAEA,CAACd,MAAM,EAAEQ,SAAS,KAAKrB,GAAG,CAACe,GAAG,CAAC,kCAAkCF,MAAM,cAAcQ,SAAS,EAAE;AACjH,CAAC;;AAED;AACA,OAAO,MAAMO,OAAO,GAAG;EACrB3B,MAAM,EAAEA,CAAC4B,IAAI,EAAEhB,MAAM,KAAKb,GAAG,CAACQ,IAAI,CAAC,gBAAgBK,MAAM,EAAE,EAAEgB,IAAI,CAAC;EAClEC,YAAY,EAAGjB,MAAM,IAAKb,GAAG,CAACe,GAAG,CAAC,cAAcF,MAAM,EAAE,CAAC;EACzDkB,cAAc,EAAEA,CAACC,EAAE,EAAEnB,MAAM,KAAKb,GAAG,CAACiC,GAAG,CAAC,SAASD,EAAE,oBAAoBnB,MAAM,EAAE,CAAC;EAChFY,MAAM,EAAEA,CAACO,EAAE,EAAEnB,MAAM,KAAKb,GAAG,CAACyB,MAAM,CAAC,SAASO,EAAE,WAAWnB,MAAM,EAAE,CAAC;EAClEG,cAAc,EAAEA,CAAA,KAAMhB,GAAG,CAACe,GAAG,CAAC,iBAAiB;AACjD,CAAC;;AAED;AACA,OAAO,MAAMmB,UAAU,GAAG;EACxBjC,MAAM,EAAEA,CAAC4B,IAAI,EAAEV,OAAO,KAAKnB,GAAG,CAACQ,IAAI,CAAC,oBAAoBW,OAAO,EAAE,EAAEU,IAAI,CAAC;EACxEM,SAAS,EAAEA,CAAA,KAAMnC,GAAG,CAACe,GAAG,CAAC,iBAAiB,CAAC;EAC3CqB,OAAO,EAAEA,CAACP,IAAI,EAAEhB,MAAM,KAAKb,GAAG,CAACQ,IAAI,CAAC,2BAA2BK,MAAM,EAAE,EAAEgB,IAAI,CAAC;EAC9EQ,YAAY,EAAEA,CAACC,QAAQ,EAAEnB,OAAO,KAAKnB,GAAG,CAACe,GAAG,CAAC,YAAYuB,QAAQ,sBAAsBnB,OAAO,EAAE,CAAC;EACjGoB,eAAe,EAAG1B,MAAM,IAAKb,GAAG,CAACe,GAAG,CAAC,iBAAiBF,MAAM,WAAW,CAAC;EACxE2B,aAAa,EAAEA,CAAC3B,MAAM,EAAEyB,QAAQ,KAAKtC,GAAG,CAACe,GAAG,CAAC,kCAAkCF,MAAM,aAAayB,QAAQ,EAAE;AAC9G,CAAC;;AAED;AACA,OAAO,MAAMG,gBAAgB,GAAG;EAC9BxC,MAAM,EAAEA,CAAC4B,IAAI,EAAEV,OAAO,KAAKnB,GAAG,CAACQ,IAAI,CAAC,0BAA0BW,OAAO,EAAE,EAAEU,IAAI,CAAC;EAC9Ea,MAAM,EAAEA,CAACC,IAAI,GAAG,IAAI,KAAK3C,GAAG,CAACe,GAAG,CAAC,iBAAiB4B,IAAI,GAAG,SAASA,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC;EAChFC,MAAM,EAAEA,CAACZ,EAAE,EAAEH,IAAI,EAAEV,OAAO,KAAKnB,GAAG,CAACiC,GAAG,CAAC,kBAAkBD,EAAE,YAAYb,OAAO,EAAE,EAAEU,IAAI,CAAC;EACvFJ,MAAM,EAAEA,CAACO,EAAE,EAAEb,OAAO,KAAKnB,GAAG,CAACyB,MAAM,CAAC,kBAAkBO,EAAE,YAAYb,OAAO,EAAE;AAC/E,CAAC;;AAED;AACA,OAAO,MAAM0B,SAAS,GAAG;EACvB5C,MAAM,EAAEA,CAAC4B,IAAI,EAAEV,OAAO,KAAKnB,GAAG,CAACQ,IAAI,CAAC,mBAAmBW,OAAO,EAAE,EAAEU,IAAI,CAAC;EACvEa,MAAM,EAAEA,CAACI,SAAS,GAAG,IAAI,EAAEC,OAAO,GAAG,IAAI,KAAK;IAC5C,IAAIC,GAAG,GAAG,SAAS;IACnB,MAAMC,MAAM,GAAG,EAAE;IACjB,IAAIH,SAAS,EAAEG,MAAM,CAACC,IAAI,CAAC,aAAaJ,SAAS,EAAE,CAAC;IACpD,IAAIC,OAAO,EAAEE,MAAM,CAACC,IAAI,CAAC,WAAWH,OAAO,EAAE,CAAC;IAC9C,IAAIE,MAAM,CAACE,MAAM,GAAG,CAAC,EAAEH,GAAG,IAAI,IAAIC,MAAM,CAACG,IAAI,CAAC,GAAG,CAAC,EAAE;IACpD,OAAOpD,GAAG,CAACe,GAAG,CAACiC,GAAG,CAAC;EACrB,CAAC;EACDK,WAAW,EAAEA,CAACC,IAAI,EAAEC,KAAK,KAAKvD,GAAG,CAACe,GAAG,CAAC,oBAAoBuC,IAAI,IAAIC,KAAK,EAAE,CAAC;EAC1EX,MAAM,EAAEA,CAACZ,EAAE,EAAEH,IAAI,EAAEV,OAAO,KAAKnB,GAAG,CAACiC,GAAG,CAAC,WAAWD,EAAE,YAAYb,OAAO,EAAE,EAAEU,IAAI,CAAC;EAChFJ,MAAM,EAAEA,CAACO,EAAE,EAAEb,OAAO,KAAKnB,GAAG,CAACyB,MAAM,CAAC,WAAWO,EAAE,YAAYb,OAAO,EAAE;AACxE,CAAC;;AAED;AACA,OAAO,MAAMqC,UAAU,GAAG;EACxBzC,GAAG,EAAGF,MAAM,IAAKb,GAAG,CAACe,GAAG,CAAC,YAAYF,MAAM,EAAE,CAAC;EAC9C+B,MAAM,EAAEA,CAAC/B,MAAM,EAAEgB,IAAI,KAAK7B,GAAG,CAACiC,GAAG,CAAC,YAAYpB,MAAM,EAAE,EAAEgB,IAAI,CAAC;EAC7D4B,WAAW,EAAG5C,MAAM,IAAKb,GAAG,CAACe,GAAG,CAAC,YAAYF,MAAM,WAAW,CAAC;EAC/D6C,cAAc,EAAEA,CAAC7C,MAAM,EAAEgB,IAAI,KAAK7B,GAAG,CAACiC,GAAG,CAAC,YAAYpB,MAAM,WAAW,EAAEgB,IAAI,CAAC;EAC9E8B,QAAQ,EAAG9C,MAAM,IAAKb,GAAG,CAACe,GAAG,CAAC,YAAYF,MAAM,QAAQ;AAC1D,CAAC;AAED,eAAeb,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}