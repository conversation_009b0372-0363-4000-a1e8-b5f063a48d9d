import React, { useState, useEffect } from 'react';
import { surveysAPI } from '../services/api';
import { useAuth } from '../context/AuthContext';
import './SurveyCreate.css';

const SurveyCreate = () => {
  const [surveys, setSurveys] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedSurvey, setSelectedSurvey] = useState(null);
  const [surveyResponses, setSurveyResponses] = useState([]);
  const [newSurvey, setNewSurvey] = useState({
    title: '',
    description: '',
    question1: '',
    question2: '',
    question3: '',
    expiryDate: ''
  });
  const { user } = useAuth();

  useEffect(() => {
    fetchSurveys();
  }, []);

  const fetchSurveys = async () => {
    try {
      setLoading(true);
      const response = await surveysAPI.getActive();
      setSurveys(response.data);
    } catch (error) {
      setError('Anketler yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateSurvey = async (e) => {
    e.preventDefault();
    try {
      await surveysAPI.create(newSurvey, user.id);
      setSuccess('Anket başarıyla oluşturuldu!');
      setNewSurvey({
        title: '',
        description: '',
        question1: '',
        question2: '',
        question3: '',
        expiryDate: ''
      });
      setShowCreateForm(false);
      fetchSurveys();
      setTimeout(() => setSuccess(''), 3000);
    } catch (error) {
      setError('Anket oluşturulurken bir hata oluştu.');
    }
  };

  const fetchSurveyResponses = async (surveyId) => {
    try {
      const response = await surveysAPI.getResponses(surveyId, user.id);
      setSurveyResponses(response.data);
      setSelectedSurvey(surveys.find(s => s.id === surveyId));
    } catch (error) {
      setError('Anket cevapları yüklenirken bir hata oluştu.');
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const isExpired = (expiryDate) => {
    if (!expiryDate) return false;
    return new Date(expiryDate) < new Date();
  };

  if (loading) {
    return (
      <div className="survey-create-container">
        <div className="loading">Anketler yükleniyor...</div>
      </div>
    );
  }

  return (
    <div className="survey-create-container">
      <div className="survey-create-header">
        <h2>📝 Anket Yönetimi</h2>
        <p>Anket oluşturun, yönetin ve cevapları görüntüleyin.</p>
        <button 
          onClick={() => setShowCreateForm(!showCreateForm)}
          className="create-survey-btn"
        >
          ➕ Yeni Anket Oluştur
        </button>
      </div>

      {error && <div className="error-message">{error}</div>}
      {success && <div className="success-message">{success}</div>}

      {showCreateForm && (
        <div className="create-survey-form">
          <h3>Yeni Anket Oluştur</h3>
          <form onSubmit={handleCreateSurvey}>
            <div className="form-group">
              <label>Anket Başlığı *</label>
              <input
                type="text"
                value={newSurvey.title}
                onChange={(e) => setNewSurvey({...newSurvey, title: e.target.value})}
                placeholder="Anket başlığını girin..."
                required
              />
            </div>
            
            <div className="form-group">
              <label>Açıklama</label>
              <textarea
                value={newSurvey.description}
                onChange={(e) => setNewSurvey({...newSurvey, description: e.target.value})}
                placeholder="Anket hakkında açıklama..."
                rows="3"
              />
            </div>
            
            <div className="form-group">
              <label>1. Soru *</label>
              <textarea
                value={newSurvey.question1}
                onChange={(e) => setNewSurvey({...newSurvey, question1: e.target.value})}
                placeholder="Birinci soruyu yazın..."
                rows="2"
                required
              />
            </div>
            
            <div className="form-group">
              <label>2. Soru *</label>
              <textarea
                value={newSurvey.question2}
                onChange={(e) => setNewSurvey({...newSurvey, question2: e.target.value})}
                placeholder="İkinci soruyu yazın..."
                rows="2"
                required
              />
            </div>
            
            <div className="form-group">
              <label>3. Soru *</label>
              <textarea
                value={newSurvey.question3}
                onChange={(e) => setNewSurvey({...newSurvey, question3: e.target.value})}
                placeholder="Üçüncü soruyu yazın..."
                rows="2"
                required
              />
            </div>
            
            <div className="form-group">
              <label>Son Cevaplama Tarihi</label>
              <input
                type="datetime-local"
                value={newSurvey.expiryDate}
                onChange={(e) => setNewSurvey({...newSurvey, expiryDate: e.target.value})}
              />
            </div>
            
            <div className="form-actions">
              <button type="submit" className="submit-btn">Anket Oluştur</button>
              <button 
                type="button" 
                onClick={() => setShowCreateForm(false)}
                className="cancel-btn"
              >
                İptal
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="surveys-section">
        <h3>📊 Mevcut Anketler ({surveys.length})</h3>
        
        {surveys.length === 0 ? (
          <div className="no-surveys">
            <div className="no-surveys-icon">📝</div>
            <h4>Henüz anket oluşturulmamış</h4>
            <p>İlk anketinizi oluşturmak için yukarıdaki butonu kullanın.</p>
          </div>
        ) : (
          <div className="surveys-grid">
            {surveys.map(survey => (
              <div key={survey.id} className={`survey-card ${isExpired(survey.expiryDate) ? 'expired' : ''}`}>
                <div className="survey-header">
                  <h4>{survey.title}</h4>
                  <div className="survey-status">
                    {isExpired(survey.expiryDate) ? (
                      <span className="status expired">⏰ Süresi Dolmuş</span>
                    ) : (
                      <span className="status active">✅ Aktif</span>
                    )}
                  </div>
                </div>
                
                {survey.description && (
                  <div className="survey-description">
                    <p>{survey.description}</p>
                  </div>
                )}
                
                <div className="survey-info">
                  <div className="info-item">
                    <span className="info-label">📅 Oluşturulma:</span>
                    <span>{formatDate(survey.createdAt)}</span>
                  </div>
                  {survey.expiryDate && (
                    <div className="info-item">
                      <span className="info-label">⏰ Son Tarih:</span>
                      <span className={isExpired(survey.expiryDate) ? 'expired-date' : ''}>
                        {formatDate(survey.expiryDate)}
                      </span>
                    </div>
                  )}
                  <div className="info-item">
                    <span className="info-label">👥 Cevap Sayısı:</span>
                    <span>{survey.responseCount}</span>
                  </div>
                </div>
                
                <div className="survey-actions">
                  <button 
                    onClick={() => fetchSurveyResponses(survey.id)}
                    className="view-responses-btn"
                  >
                    📊 Cevapları Görüntüle
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {selectedSurvey && (
        <div className="responses-modal">
          <div className="modal-content">
            <div className="modal-header">
              <h3>📊 {selectedSurvey.title} - Cevaplar</h3>
              <button 
                onClick={() => setSelectedSurvey(null)}
                className="close-btn"
              >
                ✕
              </button>
            </div>
            
            <div className="responses-content">
              <div className="responses-summary">
                <p><strong>Toplam Cevap:</strong> {surveyResponses.length}</p>
                <p><strong>Anket Tarihi:</strong> {formatDate(selectedSurvey.createdAt)}</p>
              </div>

              {surveyResponses.length === 0 ? (
                <div className="no-responses">
                  <p>Bu anket henüz cevaplanmamış.</p>
                </div>
              ) : (
                <div className="responses-list">
                  {surveyResponses.map(response => (
                    <div key={response.id} className="response-item">
                      <div className="response-header">
                        <span className="user-name">👤 {response.user.name}</span>
                        <span className="response-date">📅 {formatDate(response.createdAt)}</span>
                      </div>
                      
                      <div className="response-answers">
                        <div className="answer-group">
                          <strong>S1:</strong> {response.survey.question1}
                          <div className="answer">C: {response.answer1}</div>
                        </div>
                        <div className="answer-group">
                          <strong>S2:</strong> {response.survey.question2}
                          <div className="answer">C: {response.answer2}</div>
                        </div>
                        <div className="answer-group">
                          <strong>S3:</strong> {response.survey.question3}
                          <div className="answer">C: {response.answer3}</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SurveyCreate;
