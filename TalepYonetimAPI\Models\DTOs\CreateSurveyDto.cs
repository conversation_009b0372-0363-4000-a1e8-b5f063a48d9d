using System.ComponentModel.DataAnnotations;

namespace TalepYonetimAPI.Models.DTOs
{
    public class CreateSurveyDto
    {
        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;
        
        public string? Description { get; set; }
        
        [Required]
        [StringLength(500)]
        public string Question1 { get; set; } = string.Empty;
        
        [Required]
        [StringLength(500)]
        public string Question2 { get; set; } = string.Empty;
        
        [Required]
        [StringLength(500)]
        public string Question3 { get; set; } = string.Empty;
        
        public DateTime? ExpiryDate { get; set; }
    }
}
