{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\metin_kayit\\\\talep-yonetim-frontend\\\\src\\\\components\\\\AboutTunas.js\";\nimport React from 'react';\nimport './AboutTunas.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AboutTunas = ({\n  onClose\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"about-overlay\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"about-modal\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"about-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"about-logo\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"logo-placeholder\",\n            children: \"T\\xDCNA\\u015E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 10,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"T\\xDCRK\\u0130YE N\\xDCKLEER ENERJ\\u0130 A.\\u015E.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 12,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"T\\xFCrkiye'nin Enerji Gelece\\u011Fi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 13,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 11,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 9,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onClose,\n          className: \"close-btn\",\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"about-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-section\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hero-text\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\uD83D\\uDE80 Gelece\\u011Fin Enerjisi, Bug\\xFCn\\xFCn Teknolojisi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"T\\xDCNA\\u015E, T\\xFCrkiye'nin n\\xFCkleer enerji alan\\u0131ndaki \\xF6nc\\xFC kurulu\\u015Fu olarak, s\\xFCrd\\xFCr\\xFClebilir ve g\\xFCvenli enerji \\xE7\\xF6z\\xFCmleri geli\\u015Ftirmektedir. 25 y\\u0131l\\u0131 a\\u015Fk\\u0131n deneyimimizle, \\xFClkemizin enerji ba\\u011F\\u0131ms\\u0131zl\\u0131\\u011F\\u0131na katk\\u0131 sa\\u011Fl\\u0131yoruz.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"features-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-icon\",\n              children: \"\\uD83D\\uDD2C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Ara\\u015Ft\\u0131rma & Geli\\u015Ftirme\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 34,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"En son teknolojilerle n\\xFCkleer enerji alan\\u0131nda yenilik\\xE7i \\xE7\\xF6z\\xFCmler \\xFCretiyoruz.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-icon\",\n              children: \"\\uD83D\\uDEE1\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"G\\xFCvenlik \\xD6nceli\\u011Fi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Uluslararas\\u0131 standartlarda g\\xFCvenlik protokolleri ile \\xE7al\\u0131\\u015F\\u0131yoruz.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-icon\",\n              children: \"\\uD83C\\uDF31\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"\\xC7evre Dostu\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"S\\xFCrd\\xFCr\\xFClebilir enerji \\xFCretimi ile \\xE7evreyi koruyoruz.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-icon\",\n              children: \"\\uD83C\\uDF93\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"E\\u011Fitim & Geli\\u015Ftirme\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Uzman personel yeti\\u015Ftirme ve s\\xFCrekli e\\u011Fitim programlar\\u0131.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mission-vision\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mission\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"\\uD83C\\uDFAF Misyonumuz\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"T\\xFCrkiye'nin enerji ihtiya\\xE7lar\\u0131n\\u0131 kar\\u015F\\u0131lamak i\\xE7in g\\xFCvenli, temiz ve s\\xFCrd\\xFCr\\xFClebilir n\\xFCkleer enerji teknolojileri geli\\u015Ftirmek ve uygulamak.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"vision\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"\\uD83D\\uDD2E Vizyonumuz\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"N\\xFCkleer enerji alan\\u0131nda d\\xFCnya \\xE7ap\\u0131nda tan\\u0131nan, teknoloji lideri bir kurulu\\u015F olmak ve T\\xFCrkiye'yi enerji ba\\u011F\\u0131ms\\u0131zl\\u0131\\u011F\\u0131na kavu\\u015Fturmak.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"values-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"\\uD83D\\uDC8E De\\u011Ferlerimiz\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"values-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"value-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"value-icon\",\n                children: \"\\uD83E\\uDD1D\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"G\\xFCvenilirlik\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 80,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"value-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"value-icon\",\n                children: \"\\uD83C\\uDFAF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"M\\xFCkemmellik\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"value-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"value-icon\",\n                children: \"\\uD83C\\uDF1F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u0130novasyon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 88,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"value-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"value-icon\",\n                children: \"\\uD83E\\uDD32\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Sorumluluk\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 92,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"value-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"value-icon\",\n                children: \"\\uD83D\\uDD04\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 95,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"S\\xFCrd\\xFCr\\xFClebilirlik\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"value-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"value-icon\",\n                children: \"\\uD83D\\uDC65\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Tak\\u0131m \\xC7al\\u0131\\u015Fmas\\u0131\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"achievements\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"\\uD83C\\uDFC6 Ba\\u015Far\\u0131lar\\u0131m\\u0131z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"achievement-list\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"achievement-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"achievement-year\",\n                children: \"2023\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"achievement-text\",\n                children: \"Yenilenebilir Enerji \\xD6d\\xFCl\\xFC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"achievement-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"achievement-year\",\n                children: \"2022\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"achievement-text\",\n                children: \"En \\u0130yi Ar-Ge Projesi \\xD6d\\xFCl\\xFC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"achievement-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"achievement-year\",\n                children: \"2021\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"achievement-text\",\n                children: \"Teknoloji \\u0130novasyon \\xD6d\\xFCl\\xFC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"achievement-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"achievement-year\",\n                children: \"2020\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"achievement-text\",\n                children: \"\\xC7evre Dostu \\u015Eirket Sertifikas\\u0131\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"contact-cta\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"\\uD83D\\uDCDE Bizimle \\u0130leti\\u015Fime Ge\\xE7in\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Sorular\\u0131n\\u0131z i\\xE7in her zaman yan\\u0131n\\u0131zday\\u0131z.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"cta-buttons\",\n            children: [/*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"mailto:<EMAIL>\",\n              className: \"cta-btn primary\",\n              children: \"\\uD83D\\uDCE7 E-posta G\\xF6nder\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"tel:+903122850021\",\n              className: \"cta-btn secondary\",\n              children: \"\\uD83D\\uDCDE Hemen Ara\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = AboutTunas;\nexport default AboutTunas;\nvar _c;\n$RefreshReg$(_c, \"AboutTunas\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "AboutTunas", "onClose", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/metin_kayit/talep-yonetim-frontend/src/components/AboutTunas.js"], "sourcesContent": ["import React from 'react';\nimport './AboutTunas.css';\n\nconst AboutTunas = ({ onClose }) => {\n  return (\n    <div className=\"about-overlay\">\n      <div className=\"about-modal\">\n        <div className=\"about-header\">\n          <div className=\"about-logo\">\n            <div className=\"logo-placeholder\">TÜNAŞ</div>\n            <div>\n              <h2>TÜRKİYE NÜKLEER ENERJİ A.Ş.</h2>\n              <p>Türkiye'nin Enerji Geleceği</p>\n            </div>\n          </div>\n          <button onClick={onClose} className=\"close-btn\">&times;</button>\n        </div>\n\n        <div className=\"about-content\">\n          <div className=\"hero-section\">\n            <div className=\"hero-text\">\n              <h3>🚀 Geleceğin Enerjisi, Bugünün Teknolojisi</h3>\n              <p>\n                TÜNAŞ, Türkiye'nin nükleer enerji alanındaki öncü kuruluşu olarak, \n                sürdürülebilir ve güvenli enerji çözümleri geliştirmektedir. \n                25 yılı aşkın deneyimimizle, ülkemizin enerji bağımsızlığına katkı sağlıyoruz.\n              </p>\n            </div>\n          </div>\n\n          <div className=\"features-grid\">\n            <div className=\"feature-card\">\n              <div className=\"feature-icon\">🔬</div>\n              <h4>Araştırma & Geliştirme</h4>\n              <p>En son teknolojilerle nükleer enerji alanında yenilikçi çözümler üretiyoruz.</p>\n            </div>\n            \n            <div className=\"feature-card\">\n              <div className=\"feature-icon\">🛡️</div>\n              <h4>Güvenlik Önceliği</h4>\n              <p>Uluslararası standartlarda güvenlik protokolleri ile çalışıyoruz.</p>\n            </div>\n            \n            <div className=\"feature-card\">\n              <div className=\"feature-icon\">🌱</div>\n              <h4>Çevre Dostu</h4>\n              <p>Sürdürülebilir enerji üretimi ile çevreyi koruyoruz.</p>\n            </div>\n            \n            <div className=\"feature-card\">\n              <div className=\"feature-icon\">🎓</div>\n              <h4>Eğitim & Geliştirme</h4>\n              <p>Uzman personel yetiştirme ve sürekli eğitim programları.</p>\n            </div>\n          </div>\n\n          <div className=\"mission-vision\">\n            <div className=\"mission\">\n              <h4>🎯 Misyonumuz</h4>\n              <p>\n                Türkiye'nin enerji ihtiyaçlarını karşılamak için güvenli, temiz ve \n                sürdürülebilir nükleer enerji teknolojileri geliştirmek ve uygulamak.\n              </p>\n            </div>\n            \n            <div className=\"vision\">\n              <h4>🔮 Vizyonumuz</h4>\n              <p>\n                Nükleer enerji alanında dünya çapında tanınan, teknoloji lideri bir \n                kuruluş olmak ve Türkiye'yi enerji bağımsızlığına kavuşturmak.\n              </p>\n            </div>\n          </div>\n\n          <div className=\"values-section\">\n            <h4>💎 Değerlerimiz</h4>\n            <div className=\"values-grid\">\n              <div className=\"value-item\">\n                <span className=\"value-icon\">🤝</span>\n                <span>Güvenilirlik</span>\n              </div>\n              <div className=\"value-item\">\n                <span className=\"value-icon\">🎯</span>\n                <span>Mükemmellik</span>\n              </div>\n              <div className=\"value-item\">\n                <span className=\"value-icon\">🌟</span>\n                <span>İnovasyon</span>\n              </div>\n              <div className=\"value-item\">\n                <span className=\"value-icon\">🤲</span>\n                <span>Sorumluluk</span>\n              </div>\n              <div className=\"value-item\">\n                <span className=\"value-icon\">🔄</span>\n                <span>Sürdürülebilirlik</span>\n              </div>\n              <div className=\"value-item\">\n                <span className=\"value-icon\">👥</span>\n                <span>Takım Çalışması</span>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"achievements\">\n            <h4>🏆 Başarılarımız</h4>\n            <div className=\"achievement-list\">\n              <div className=\"achievement-item\">\n                <span className=\"achievement-year\">2023</span>\n                <span className=\"achievement-text\">Yenilenebilir Enerji Ödülü</span>\n              </div>\n              <div className=\"achievement-item\">\n                <span className=\"achievement-year\">2022</span>\n                <span className=\"achievement-text\">En İyi Ar-Ge Projesi Ödülü</span>\n              </div>\n              <div className=\"achievement-item\">\n                <span className=\"achievement-year\">2021</span>\n                <span className=\"achievement-text\">Teknoloji İnovasyon Ödülü</span>\n              </div>\n              <div className=\"achievement-item\">\n                <span className=\"achievement-year\">2020</span>\n                <span className=\"achievement-text\">Çevre Dostu Şirket Sertifikası</span>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"contact-cta\">\n            <h4>📞 Bizimle İletişime Geçin</h4>\n            <p>Sorularınız için her zaman yanınızdayız.</p>\n            <div className=\"cta-buttons\">\n              <a href=\"mailto:<EMAIL>\" className=\"cta-btn primary\">\n                📧 E-posta Gönder\n              </a>\n              <a href=\"tel:+903122850021\" className=\"cta-btn secondary\">\n                📞 Hemen Ara\n              </a>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AboutTunas;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,UAAU,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAClC,oBACEF,OAAA;IAAKG,SAAS,EAAC,eAAe;IAAAC,QAAA,eAC5BJ,OAAA;MAAKG,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BJ,OAAA;QAAKG,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BJ,OAAA;UAAKG,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBJ,OAAA;YAAKG,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC7CR,OAAA;YAAAI,QAAA,gBACEJ,OAAA;cAAAI,QAAA,EAAI;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpCR,OAAA;cAAAI,QAAA,EAAG;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNR,OAAA;UAAQS,OAAO,EAAEP,OAAQ;UAACC,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,eAENR,OAAA;QAAKG,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BJ,OAAA;UAAKG,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BJ,OAAA;YAAKG,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBJ,OAAA;cAAAI,QAAA,EAAI;YAA0C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnDR,OAAA;cAAAI,QAAA,EAAG;YAIH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENR,OAAA;UAAKG,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BJ,OAAA;YAAKG,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BJ,OAAA;cAAKG,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCR,OAAA;cAAAI,QAAA,EAAI;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/BR,OAAA;cAAAI,QAAA,EAAG;YAA4E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChF,CAAC,eAENR,OAAA;YAAKG,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BJ,OAAA;cAAKG,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvCR,OAAA;cAAAI,QAAA,EAAI;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1BR,OAAA;cAAAI,QAAA,EAAG;YAAiE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC,eAENR,OAAA;YAAKG,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BJ,OAAA;cAAKG,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCR,OAAA;cAAAI,QAAA,EAAI;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBR,OAAA;cAAAI,QAAA,EAAG;YAAoD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eAENR,OAAA;YAAKG,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BJ,OAAA;cAAKG,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCR,OAAA;cAAAI,QAAA,EAAI;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5BR,OAAA;cAAAI,QAAA,EAAG;YAAwD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENR,OAAA;UAAKG,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BJ,OAAA;YAAKG,SAAS,EAAC,SAAS;YAAAC,QAAA,gBACtBJ,OAAA;cAAAI,QAAA,EAAI;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtBR,OAAA;cAAAI,QAAA,EAAG;YAGH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENR,OAAA;YAAKG,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACrBJ,OAAA;cAAAI,QAAA,EAAI;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtBR,OAAA;cAAAI,QAAA,EAAG;YAGH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENR,OAAA;UAAKG,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BJ,OAAA;YAAAI,QAAA,EAAI;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxBR,OAAA;YAAKG,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BJ,OAAA;cAAKG,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBJ,OAAA;gBAAMG,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtCR,OAAA;gBAAAI,QAAA,EAAM;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACNR,OAAA;cAAKG,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBJ,OAAA;gBAAMG,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtCR,OAAA;gBAAAI,QAAA,EAAM;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACNR,OAAA;cAAKG,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBJ,OAAA;gBAAMG,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtCR,OAAA;gBAAAI,QAAA,EAAM;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACNR,OAAA;cAAKG,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBJ,OAAA;gBAAMG,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtCR,OAAA;gBAAAI,QAAA,EAAM;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,eACNR,OAAA;cAAKG,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBJ,OAAA;gBAAMG,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtCR,OAAA;gBAAAI,QAAA,EAAM;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACNR,OAAA;cAAKG,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBJ,OAAA;gBAAMG,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtCR,OAAA;gBAAAI,QAAA,EAAM;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENR,OAAA;UAAKG,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BJ,OAAA;YAAAI,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzBR,OAAA;YAAKG,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BJ,OAAA;cAAKG,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BJ,OAAA;gBAAMG,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9CR,OAAA;gBAAMG,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAA0B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CAAC,eACNR,OAAA;cAAKG,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BJ,OAAA;gBAAMG,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9CR,OAAA;gBAAMG,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAA0B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CAAC,eACNR,OAAA;cAAKG,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BJ,OAAA;gBAAMG,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9CR,OAAA;gBAAMG,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAAyB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC,eACNR,OAAA;cAAKG,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BJ,OAAA;gBAAMG,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9CR,OAAA;gBAAMG,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAA8B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENR,OAAA;UAAKG,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BJ,OAAA;YAAAI,QAAA,EAAI;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnCR,OAAA;YAAAI,QAAA,EAAG;UAAwC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC/CR,OAAA;YAAKG,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BJ,OAAA;cAAGU,IAAI,EAAC,qCAAqC;cAACP,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAE1E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJR,OAAA;cAAGU,IAAI,EAAC,mBAAmB;cAACP,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAE1D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACG,EAAA,GA3IIV,UAAU;AA6IhB,eAAeA,UAAU;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}