.survey-create-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.survey-create-header {
  text-align: center;
  margin-bottom: 2rem;
  padding: 2rem;
  background: linear-gradient(135deg, #fef7ff 0%, #f3e8ff 100%);
  border-radius: 16px;
  border-left: 5px solid #8b5cf6;
}

.survey-create-header h2 {
  margin: 0 0 0.5rem 0;
  color: var(--tunas-primary);
  font-size: 2rem;
}

.survey-create-header p {
  margin: 0 0 1.5rem 0;
  color: var(--tunas-gray);
  font-size: 1.1rem;
}

.create-survey-btn {
  background: #8b5cf6;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.create-survey-btn:hover {
  background: #7c3aed;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.loading {
  text-align: center;
  padding: 3rem;
  font-size: 1.2rem;
  color: var(--tunas-gray);
}

.error-message {
  background-color: #fef2f2;
  color: #dc2626;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  border-left: 4px solid #dc2626;
}

.success-message {
  background-color: #f0fdf4;
  color: #16a34a;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  border-left: 4px solid #16a34a;
}

.create-survey-form {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 2rem;
  border-left: 5px solid #8b5cf6;
}

.create-survey-form h3 {
  margin: 0 0 1.5rem 0;
  color: var(--tunas-primary);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--tunas-primary);
  font-weight: 500;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #8b5cf6;
}

.form-group textarea {
  resize: vertical;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.submit-btn {
  background: #8b5cf6;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.submit-btn:hover {
  background: #7c3aed;
}

.cancel-btn {
  background: #6b7280;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  background: #4b5563;
}

.surveys-section h3 {
  color: var(--tunas-primary);
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
}

.no-surveys {
  text-align: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.no-surveys-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-surveys h4 {
  color: var(--tunas-primary);
  margin-bottom: 1rem;
}

.no-surveys p {
  color: var(--tunas-gray);
  max-width: 400px;
  margin: 0 auto;
  line-height: 1.6;
}

.surveys-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
}

.survey-card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border-left: 5px solid #8b5cf6;
  transition: all 0.3s ease;
}

.survey-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.survey-card.expired {
  border-left-color: #dc2626;
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
}

.survey-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.survey-header h4 {
  margin: 0;
  color: var(--tunas-primary);
  font-size: 1.3rem;
  flex: 1;
}

.survey-status {
  margin-left: 1rem;
}

.status {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status.active {
  background-color: #d1fae5;
  color: #065f46;
}

.status.expired {
  background-color: #fee2e2;
  color: #991b1b;
}

.survey-description {
  margin-bottom: 1rem;
  padding: 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 8px;
}

.survey-description p {
  margin: 0;
  color: var(--tunas-dark);
  line-height: 1.6;
}

.survey-info {
  display: grid;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.info-item {
  display: flex;
  gap: 0.5rem;
}

.info-label {
  font-weight: 500;
  color: var(--tunas-primary);
  min-width: 120px;
}

.expired-date {
  color: #dc2626;
  font-weight: 500;
}

.survey-actions {
  display: flex;
  justify-content: flex-end;
}

.view-responses-btn {
  background: #f59e0b;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.view-responses-btn:hover {
  background: #d97706;
  transform: translateY(-1px);
}

.responses-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e5e7eb;
}

.modal-header h3 {
  margin: 0;
  color: var(--tunas-primary);
  font-size: 1.5rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
  color: var(--tunas-gray);
}

.close-btn:hover {
  background-color: #f3f4f6;
  color: var(--tunas-primary);
}

.responses-summary {
  margin-bottom: 2rem;
  padding: 1rem;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 8px;
}

.responses-summary p {
  margin: 0.5rem 0;
  color: var(--tunas-primary);
}

.no-responses {
  text-align: center;
  padding: 2rem;
  color: var(--tunas-gray);
}

.responses-list {
  display: grid;
  gap: 1.5rem;
}

.response-item {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 1.5rem;
  border-radius: 12px;
  border-left: 4px solid #8b5cf6;
}

.response-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.user-name {
  font-weight: 500;
  color: var(--tunas-primary);
}

.response-date {
  color: var(--tunas-gray);
  font-size: 0.9rem;
}

.response-answers {
  display: grid;
  gap: 1rem;
}

.answer-group strong {
  color: var(--tunas-primary);
  display: block;
  margin-bottom: 0.25rem;
}

.answer {
  color: var(--tunas-dark);
  font-style: italic;
  padding-left: 1rem;
  border-left: 2px solid #8b5cf6;
  margin-top: 0.5rem;
}

/* Responsive */
@media (max-width: 768px) {
  .survey-create-container {
    padding: 1rem 0.5rem;
  }
  
  .survey-create-header {
    padding: 1.5rem;
  }
  
  .survey-create-header h2 {
    font-size: 1.5rem;
  }
  
  .surveys-grid {
    grid-template-columns: 1fr;
  }
  
  .survey-header {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .survey-status {
    margin-left: 0;
    align-self: flex-start;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .modal-content {
    margin: 1rem;
    padding: 1.5rem;
  }
  
  .response-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
