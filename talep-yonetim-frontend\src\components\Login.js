import React, { useState } from 'react';
import { authAPI } from '../services/api';
import { useAuth } from '../context/AuthContext';
import './Login.css';

const Login = ({ onSwitchToRegister }) => {
  const [formData, setFormData] = useState({
    employeeNumber: '',
    password: ''
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const { login } = useAuth();

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const response = await authAPI.login(formData.employeeNumber, formData.password);
      login(response.data.user);
    } catch (error) {
      setError(error.response?.data?.message || '<PERSON><PERSON><PERSON> ya<PERSON>ılırken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-container">
      <div className="login-form">
        <div className="login-header">
          <div className="company-logo">
            <div className="logo-placeholder">TÜNAŞ</div>
            <div className="company-info">
              <h2>Talep Yönetim Sistemi</h2>
              <p>Türkiye Nükleer Enerji A.Ş.</p>
            </div>
          </div>
        </div>
        {error && <div className="error-message">{error}</div>}
        
        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="employeeNumber">Personel Numarası:</label>
            <input
              type="text"
              id="employeeNumber"
              name="employeeNumber"
              value={formData.employeeNumber}
              onChange={handleChange}
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="password">Şifre:</label>
            <input
              type="password"
              id="password"
              name="password"
              value={formData.password}
              onChange={handleChange}
              required
            />
          </div>

          <button type="submit" disabled={loading} className="submit-btn">
            {loading ? 'Giriş yapılıyor...' : 'Giriş Yap'}
          </button>
        </form>

        <p className="switch-form">
          Hesabınız yok mu? 
          <button type="button" onClick={onSwitchToRegister} className="link-btn">
            Kayıt Ol
          </button>
        </p>
      </div>
    </div>
  );
};

export default Login;
