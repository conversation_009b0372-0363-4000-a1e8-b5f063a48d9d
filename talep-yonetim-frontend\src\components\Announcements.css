.announcements-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.announcements-header {
  text-align: center;
  margin-bottom: 2rem;
  padding: 2rem;
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border-radius: 16px;
  border-left: 5px solid #f59e0b;
}

.announcements-header h2 {
  margin: 0 0 0.5rem 0;
  color: var(--tunas-primary);
  font-size: 2rem;
}

.announcements-header p {
  margin: 0;
  color: var(--tunas-gray);
  font-size: 1.1rem;
}

.loading {
  text-align: center;
  padding: 3rem;
  font-size: 1.2rem;
  color: var(--tunas-gray);
}

.error-message {
  background-color: #fef2f2;
  color: #dc2626;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  border-left: 4px solid #dc2626;
}

.announcement-tabs {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  border-bottom: 2px solid #e5e7eb;
}

.tab-btn {
  background: none;
  border: none;
  padding: 1rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
  color: var(--tunas-gray);
}

.tab-btn.active {
  color: var(--tunas-primary);
  border-bottom-color: #f59e0b;
}

.tab-btn:hover {
  color: var(--tunas-primary);
  background-color: #f8fafc;
}

.no-announcements {
  text-align: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.no-announcements-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-announcements h3 {
  color: var(--tunas-primary);
  margin-bottom: 1rem;
}

.no-announcements p {
  color: var(--tunas-gray);
  max-width: 400px;
  margin: 0 auto;
  line-height: 1.6;
}

.announcements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
}

.announcement-card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border-left: 5px solid #f59e0b;
  transition: all 0.3s ease;
}

.announcement-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.announcement-card.important {
  border-left-color: #dc2626;
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
}

.announcement-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.announcement-type {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: var(--tunas-primary);
}

.important-badge {
  background: #dc2626;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.announcement-date {
  color: var(--tunas-gray);
  font-size: 0.9rem;
}

.announcement-content h3 {
  margin: 0 0 1rem 0;
  color: var(--tunas-primary);
  font-size: 1.3rem;
  line-height: 1.4;
}

.announcement-content p {
  margin: 0 0 1rem 0;
  color: var(--tunas-dark);
  line-height: 1.6;
}

.announcement-meta {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.admin-name,
.updated-date {
  color: var(--tunas-gray);
  font-size: 0.9rem;
}

.announcement-actions {
  display: flex;
  justify-content: flex-end;
}

.read-more-btn {
  background: #f59e0b;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.read-more-btn:hover {
  background: #d97706;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.announcement-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  max-width: 700px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e5e7eb;
}

.modal-title {
  flex: 1;
}

.modal-type {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--tunas-primary);
}

.modal-title h3 {
  margin: 0;
  color: var(--tunas-primary);
  font-size: 1.5rem;
  line-height: 1.4;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
  color: var(--tunas-gray);
}

.close-btn:hover {
  background-color: #f3f4f6;
  color: var(--tunas-primary);
}

.modal-meta {
  display: grid;
  gap: 0.75rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
}

.meta-item {
  display: flex;
  gap: 0.5rem;
}

.meta-label {
  font-weight: 500;
  color: var(--tunas-primary);
  min-width: 120px;
}

.modal-content-text {
  margin-bottom: 2rem;
  line-height: 1.8;
  color: var(--tunas-dark);
}

.modal-content-text p {
  margin-bottom: 1rem;
}

.modal-content-text p:last-child {
  margin-bottom: 0;
}

.modal-actions {
  display: flex;
  justify-content: center;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.close-modal-btn {
  background: var(--tunas-primary);
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 25px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-modal-btn:hover {
  background: var(--tunas-secondary);
  transform: translateY(-1px);
}

/* Responsive */
@media (max-width: 768px) {
  .announcements-container {
    padding: 1rem 0.5rem;
  }
  
  .announcements-header {
    padding: 1.5rem;
  }
  
  .announcements-header h2 {
    font-size: 1.5rem;
  }
  
  .announcements-grid {
    grid-template-columns: 1fr;
  }
  
  .announcement-tabs {
    flex-direction: column;
  }
  
  .tab-btn {
    text-align: left;
  }
  
  .announcement-header {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .announcement-meta {
    gap: 0.25rem;
  }
  
  .modal-content {
    margin: 1rem;
    padding: 1.5rem;
  }
  
  .modal-header {
    flex-direction: column;
    gap: 1rem;
  }
  
  .modal-meta {
    padding: 1rem;
  }
  
  .meta-item {
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .meta-label {
    min-width: auto;
  }
}
